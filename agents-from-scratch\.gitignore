# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.secrets/

# Jupyter Notebook
.ipynb_checkpoints
.langgraph_checkpoint
*.ipynb
!notebooks/*.ipynb
!notebooks/notebooks/*.ipynb
!tests/*.ipynb

# Virtual Environment
.venv
venv/
env/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Distribution
*.tar.gz
*.whl

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.langgraph_api
