{"cells": [{"cell_type": "code", "execution_count": 1, "id": "17903680", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.environ.get(\"GOOGLE_API_KEY\"):\n", "  os.environ[\"GOOGLE_API_KEY\"] = getpass.getpass(\"Enter API key for Google Gemini: \")\n", "\n", "from langchain.chat_models import init_chat_model\n", "\n", "llm = init_chat_model(\"gemini-2.5-pro\", model_provider=\"google_genai\")"]}, {"cell_type": "code", "execution_count": 2, "id": "01a8be2d", "metadata": {}, "outputs": [], "source": ["\n", "if not os.environ.get(\"GOOGLE_API_KEY\"):\n", "  os.environ[\"GOOGLE_API_KEY\"] = getpass.getpass(\"Enter API key for Google Gemini: \")\n", "\n", "from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "\n", "embeddings = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")"]}, {"cell_type": "code", "execution_count": 3, "id": "368e7a46", "metadata": {}, "outputs": [], "source": ["if not os.environ.get(\"TAVILY_API_KEY\"):\n", "  os.environ[\"TAVILY_API_KEY\"] = getpass.getpass(\"Enter API key for Tavily: \")"]}, {"cell_type": "code", "execution_count": 4, "id": "299530af", "metadata": {}, "outputs": [], "source": ["import chromadb\n", "\n", "client = chromadb.PersistentClient(path=\"./chroma_langchain_db\")"]}, {"cell_type": "code", "execution_count": 5, "id": "fe1357f0", "metadata": {}, "outputs": [], "source": ["from langchain_chroma import Chroma\n", "\n", "vector_store = Chroma(\n", "    client=client,\n", "    collection_name=\"example_collection\",\n", "    embedding_function=embeddings,\n", "    # persist_directory=\"./chroma_langchain_db\",  # Where to save data locally, remove if not necessary ( in upper I decler the client so we need this)\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "abbe8008", "metadata": {}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "from typing import Annotated\n", "from langgraph.graph.message import add_messages\n", "\n", "\n", "class State(TypedDict):\n", "    # Messages have the type \"list\". The `add_messages` function\n", "    # in the annotation defines how this state key should be updated\n", "    # (in this case, it appends messages to the list, rather than overwriting them)\n", "    messages: Annotated[list, add_messages]"]}, {"cell_type": "code", "execution_count": 7, "id": "31e7e368", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'query': \"What's a 'node' in LangGraph?\",\n", " 'follow_up_questions': None,\n", " 'answer': None,\n", " 'images': [],\n", " 'results': [{'url': 'https://medium.com/@vivekvjnk/langgraph-basics-understanding-state-schema-nodes-and-edges-77f2fd17cae5',\n", "   'title': 'LangGraph Basics: Understanding State, Schema, Nodes, and Edges',\n", "   'content': 'Nodes: Perform the actual work. Nodes contain Python code that can execute any logic, from simple computations to LLM calls or integrations.',\n", "   'score': 0.79254496,\n", "   'raw_content': None},\n", "  {'url': 'https://medium.com/@cplog/introduction-to-langgraph-a-beginners-guide-14f9be027141',\n", "   'title': \"Introduction to <PERSON><PERSON><PERSON><PERSON>: A Beginner's Guide - Medium\",\n", "   'content': '*   **Stateful Graph:** LangGraph revolves around the concept of a stateful graph, where each node in the graph represents a step in your computation, and the graph maintains a state that is passed around and updated as the computation progresses. LangGraph supports conditional edges, allowing you to dynamically determine the next node to execute based on the current state of the graph. Image 10: Introduction to AI Agent with LangChain and LangGraph: A Beginner’s Guide Image 18: How to build LLM Agent with LangGraph\\u200a—\\u200aStateGraph and Reducer Image 20: Simplest Graphs using LangGraph Framework Image 24: Building a ReAct Agent with Lang<PERSON>: A Step-by-Step Guide Image 28: Building an Agentic RAG with LangGraph: A Step-by-Step Guide',\n", "   'score': 0.65279555,\n", "   'raw_content': None},\n", "  {'url': 'https://langchain-ai.github.io/langgraph/concepts/low_level/',\n", "   'title': 'LangGraph Glossary - GitHub Pages',\n", "   'content': 'To build your graph, you first define the state, you then add nodes and edges, and then you compile it. The schema of the `State` will be the input schema to all `Nodes` and `Edges` in the graph, and can be either a `TypedDict` or a `Pydantic` model. The reducer function is vital to telling the graph how to update the list of `Message` objects in the state with each state update (for example, when a node sends an update). graph.add_conditional_edges(\"node_a\", routing_function) Use `Command` when you need to **both** update the graph state **and** route to a different node. When you send updates from a subgraph node to a parent graph node for a key that\\'s shared by both parent and subgraph state schemas, you **must** define a reducer for the key you\\'re updating in the parent graph state.',\n", "   'score': 0.4042963,\n", "   'raw_content': None}],\n", " 'response_time': 1.53}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_tavily import TavilySearch\n", "\n", "tool = TavilySearch(max_results=3)\n", "tools = [tool]\n", "tool.invoke(\"What's a 'node' in LangGraph?\")"]}, {"cell_type": "code", "execution_count": 8, "id": "********", "metadata": {}, "outputs": [], "source": ["llm_with_tools = llm.bind_tools(tools)"]}, {"cell_type": "code", "execution_count": 9, "id": "350c4bcf", "metadata": {}, "outputs": [], "source": ["\n", "def <PERSON><PERSON><PERSON><PERSON>(state: State):\n", "    aiMessage = llm_with_tools.invoke(state[\"messages\"])\n", "    return {\"messages\": [aiMessage]}\n"]}, {"cell_type": "code", "execution_count": 10, "id": "45721b9b", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import START, MessagesState, StateGraph, END\n", "from langgraph.prebuilt import ToolNode, tools_condition\n", "\n", "\n", "workflow = StateGraph(MessagesState)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "3ec519f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x1c9495ed400>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["tool_node = ToolNode(tools=[tool])\n", "\n", "workflow.add_node(\"chatbot\", ChatBot)\n", "workflow.add_node(\"tools\", tool_node)\n", "\n", "workflow.add_conditional_edges(\"chatbot\", tools_condition)\n", "workflow.add_edge('tools', 'chatbot')\n", "\n", "workflow.add_edge(START, \"chatbot\")\n", "workflow.add_edge(\"chatbot\", END)"]}, {"cell_type": "code", "execution_count": 12, "id": "c3a633a6", "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "memory = InMemorySaver()"]}, {"cell_type": "code", "execution_count": 13, "id": "249f47a2", "metadata": {}, "outputs": [], "source": ["app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 14, "id": "416ec09c", "metadata": {}, "outputs": [], "source": ["config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "def Chat_with_llm(user_input : str):\n", "    output = \"\"\n", "    for event in app.stream({\"messages\": [{\"role\": \"user\", \"content\": user_input}]}, config=config, stream_mode=\"values\"):\n", "        # print(event[\"chatbot\"][\"messages\"][0].content)\n", "        # print(type(event))\n", "        # for value in event.values():\n", "        #     print(value)\n", "        output = (event[\"messages\"][-1].content)\n", "    return output\n"]}, {"cell_type": "code", "execution_count": 15, "id": "38c3e11f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'I am a large language model, trained by Google.'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["Chat_with_llm(\"What you're name\")"]}, {"cell_type": "code", "execution_count": 16, "id": "98c460ce", "metadata": {}, "outputs": [], "source": ["result = app.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"Is women voice is help to reduece man stress\"}]}, config=config)\n"]}, {"cell_type": "code", "execution_count": 17, "id": "e7723c2a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What you're name\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I am a large language model, trained by Google.\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Is women voice is help to reduece man stress\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  tavily_search (5fd9dca2-8212-414c-a744-10d96f865b25)\n", " Call ID: 5fd9dca2-8212-414c-a744-10d96f865b25\n", "  Args:\n", "    query: Does a woman's voice help to reduce man's stress\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"Does a woman's voice help to reduce man's stress\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://m.facebook.com/MindsssCanvas/photos/did-you-know-that-simply-hearing-a-womans-voice-can-physically-calm-a-mans-nervo/722459307615348/\", \"title\": \"Mind's - Did you know that simply hearing a woman's voice can ...\", \"content\": \"Did you know that simply hearing a woman's voice can physically calm a man's nervous system? Science shows that the sound of a female voice\", \"score\": 0.7348247, \"raw_content\": null}, {\"url\": \"https://www.instagram.com/reel/DMhwj1Jzhew/\", \"title\": \"Studies find that a woman's voice can literally stimulate the vagus ...\", \"content\": \"... can contribute to a sense of security and reduce anxiety, leading to a lower heart rate. One study even found that female voices are\", \"score\": 0.69573146, \"raw_content\": null}, {\"url\": \"https://www.facebook.com/MindsssCanvas/posts/did-you-know-that-simply-hearing-a-womans-voice-can-physically-calm-a-mans-nervo/722459320948680/\", \"title\": \"Did you know that simply hearing a woman's voice can physically ...\", \"content\": \"Hearing a female voice can therefore signal to the brain that the environment is safe, reducing anxiety and lowering heart rate. Research also\", \"score\": 0.68140936, \"raw_content\": null}], \"response_time\": 1.23}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Yes, some studies and scientific theories suggest that a woman's voice can have a calming effect on men and may help reduce stress. The sound of a female voice can contribute to a sense of security and reduce anxiety, which can lead to a lower heart rate. The idea is that a female voice can signal to the brain that the environment is safe, which in turn helps to lower stress levels.\n"]}], "source": ["for m in result[\"messages\"]:\n", "    m.pretty_print()"]}, {"cell_type": "code", "execution_count": 18, "id": "<PERSON><PERSON><PERSON>", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(app.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "code", "execution_count": 35, "id": "4e94f429", "metadata": {}, "outputs": [], "source": ["config2 = {\"configurable\": {\"thread_id\": \"2\"}}"]}, {"cell_type": "code", "execution_count": null, "id": "9707fbbc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 36, "id": "8b635e75", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='What is my name can you remember?', additional_kwargs={}, response_metadata={}, id='218a69dc-8b15-45d4-9a48-50f3ec49ba4b'),\n", "  AIMessage(content='I cannot remember your name. I am a large language model, trained by Google, and I do not have access to any personal information. I am also unable to store information from previous conversations.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--bca78c46-23b9-46a6-9566-6a5954e6d643-0', usage_metadata={'input_tokens': 1248, 'output_tokens': 39, 'total_tokens': 1287, 'input_token_details': {'cache_read': 0}}),\n", "  HumanMessage(content='What is my name', additional_kwargs={}, response_metadata={}, id='66180137-acab-42e8-a341-de545ce505b8'),\n", "  AIMessage(content='I do not have access to your name. I am a large language model and do not have the ability to access your personal information.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--1edf5c23-48ad-42d5-94d2-cb72c4cef07e-0', usage_metadata={'input_tokens': 1293, 'output_tokens': 204, 'total_tokens': 1497, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 177}}),\n", "  HumanMessage(content='Remember my name is subhadip', additional_kwargs={}, response_metadata={}, id='6f81315b-07e8-40f0-86e7-8be668fdf3cc'),\n", "  AIMessage(content=\"I will do my best to remember that your name is <PERSON><PERSON><PERSON> for the rest of our current conversation. However, please understand that I am a large language model, and I won't be able to recall it in future sessions.\", additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--cdf0d045-ae41-495e-8fe2-60d44a3593a4-0', usage_metadata={'input_tokens': 1329, 'output_tokens': 308, 'total_tokens': 1637, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 260}}),\n", "  HumanMessage(content='What is my name', additional_kwargs={}, response_metadata={}, id='0e096fdd-86a7-49ab-aedf-62e00125e11e'),\n", "  AIMessage(content='Your name is Subhadip.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--c1d7a555-cfb2-4091-855d-90cda07a2c7b-0', usage_metadata={'input_tokens': 1383, 'output_tokens': 144, 'total_tokens': 1527, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 137}}),\n", "  HumanMessage(content='What is my name', additional_kwargs={}, response_metadata={}, id='e24f97bb-b29e-4058-93db-da46fdb6b923'),\n", "  AIMessage(content='Your name is Subhadip.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--2946b082-ccbb-46d3-9513-20f28751cc73-0', usage_metadata={'input_tokens': 1396, 'output_tokens': 165, 'total_tokens': 1561, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 158}}),\n", "  HumanMessage(content='What is my name', additional_kwargs={}, response_metadata={}, id='8985b949-17ba-4c9f-9a77-fc7d2f05b19b'),\n", "  AIMessage(content='Your name is Subhadip.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--ad2be17f-b108-4413-b6e0-459135e37d3a-0', usage_metadata={'input_tokens': 1409, 'output_tokens': 67, 'total_tokens': 1476, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 60}}),\n", "  HumanMessage(content='What is my name', additional_kwargs={}, response_metadata={}, id='895487f2-0503-4c3b-afa1-848026cdcc26'),\n", "  AIMessage(content='Your name is Subhadip.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--cd71f850-16cd-4239-8dff-905ba280c1b7-0', usage_metadata={'input_tokens': 1422, 'output_tokens': 105, 'total_tokens': 1527, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 98}}),\n", "  HumanMessage(content='I give you a name Minu', additional_kwargs={}, response_metadata={}, id='1a270f63-b1f6-4a5d-a98c-c83c99c28c91'),\n", "  AIMessage(content='Got it. I will try to remember that your name is <PERSON><PERSON> for the rest of our conversation.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--e7bd4a0d-b618-42e4-9c4d-32ef6756a548-0', usage_metadata={'input_tokens': 1438, 'output_tokens': 200, 'total_tokens': 1638, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 179}}),\n", "  HumanMessage(content=\"what is you're name\", additional_kwargs={}, response_metadata={}, id='c3ad2bea-9870-40d7-be4e-3b3518bf4dc9'),\n", "  AIMessage(content='I do not have a name. I am a large language model, trained by Google.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--b1f2b8bc-7ce5-4901-aa09-bb822d6d0e1f-0', usage_metadata={'input_tokens': 1467, 'output_tokens': 149, 'total_tokens': 1616, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 131}}),\n", "  HumanMessage(content=\"I give you're name minu remember it\", additional_kwargs={}, response_metadata={}, id='42aed6f4-55f0-484d-b8c6-ef09e7412846'),\n", "  AIMessage(content='I am a large language model and do not have a name. However, you can call me <PERSON><PERSON> if you would like.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--5b525202-c9f3-45e2-90fa-d85c3002fa23-0', usage_metadata={'input_tokens': 1496, 'output_tokens': 257, 'total_tokens': 1753, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 231}}),\n", "  HumanMessage(content=\"what is you're name\", additional_kwargs={}, response_metadata={}, id='123db131-90ba-4523-a81a-a8d3e2dd4c97'),\n", "  AIMessage(content='You can call me Minu. I am a large language model, trained by Google.', additional_kwargs={}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--3a8745bd-7eb6-4dc1-801e-d6af75d8c603-0', usage_metadata={'input_tokens': 1530, 'output_tokens': 175, 'total_tokens': 1705, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 157}})]}"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"what is you're name\"}]}, config=config2)"]}, {"cell_type": "code", "execution_count": null, "id": "0efc675e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "id": "b08ec88a", "metadata": {}, "outputs": [], "source": ["import config\n", "\n", "async def get_client():\n", "    client = TelegramClient(\n", "        session=config.SESSION_NAME,\n", "        api_hash=config.API_HASH,\n", "        api_id=config.API_ID,\n", "    )\n", "    await client.start()\n", "    \n", "    if not await client.is_user_authorized():\n", "        await client.send_code_request(config.PHONE_NUMBER)\n", "        code = input('Enter the code: ')\n", "        await client.sign_in(config.PHONE_NUMBER, code)\n", "        \n", "    return client"]}, {"cell_type": "code", "execution_count": null, "id": "cfe00298", "metadata": {}, "outputs": [], "source": ["class TeleBot:\n", "    def __init__(self, tg_client):\n", "        self.tg_client = tg_client\n", "        self._handler = None\n", "\n", "    async def rawMessage(self, channel_link: str, message_limit: int):\n", "        try:\n", "            channel_entity = await self.tg_client.get_entity(channel_link)\n", "            messages = self.tg_client.iter_messages(channel_entity, limit=message_limit)\n", "            return messages\n", "        \n", "        except Flood<PERSON>aitError as e:\n", "            print(f\"FloodWaitError: Wait {e.seconds} seconds.\")\n", "            await asyncio.sleep(e.seconds)\n", "            \n", "        except Exception as e:\n", "            print(f\"Error getting channel messages: {e}\")\n", "            return None\n"]}, {"cell_type": "code", "execution_count": null, "id": "ccafb813", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import START, MessagesState, StateGraph\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "stat_builder = StateGraph(MessagesState)"]}, {"cell_type": "code", "execution_count": 27, "id": "88952d5c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\langsmith\\client.py:272: LangSmithMissingAPIKeyWarning: API key must be provided when using hosted LangSmith API\n", "  warnings.warn(\n"]}], "source": ["# Index chunks\n", "_ = vector_store.add_documents(documents=all_splits)\n", "\n", "# Define prompt for question-answering\n", "# N.B. for non-US LangSmith endpoints, you may need to specify\n", "# api_url=\"https://api.smith.langchain.com\" in hub.pull.\n", "prompt = hub.pull(\"rlm/rag-prompt\")"]}, {"cell_type": "code", "execution_count": 28, "id": "62e52185", "metadata": {}, "outputs": [], "source": ["\n", "# Define state for application\n", "class State(TypedDict):\n", "    question: str\n", "    context: List[Document]\n", "    answer: str"]}, {"cell_type": "code", "execution_count": 36, "id": "26f16be4", "metadata": {}, "outputs": [], "source": ["# Define application steps\n", "from langchain_core.tools import tool\n", "from langgraph.graph import MessagesState, StateGraph\n", "\n", "\n", "@tool(response_format=\"content_and_artifact\")\n", "def retrieve(query: str):\n", "    \"\"\"Retrieve information related to a query.\"\"\"\n", "    retrieved_docs = vector_store.similarity_search(query, k=2)\n", "    serialized = \"\\n\\n\".join(\n", "        (f\"Source: {doc.metadata}\\nContent: {doc.page_content}\")\n", "        for doc in retrieved_docs\n", "    )\n", "    return serialized, retrieved_docs\n", "\n", "\n", "\n", "def generate(state: State):\n", "    docs_content = \"\\n\\n\".join(doc.page_content for doc in state[\"context\"])\n", "    messages = prompt.invoke({\"question\": state[\"question\"], \"context\": docs_content})\n", "    response = llm.invoke(messages)\n", "    return {\"answer\": response.content}\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 35, "id": "8ad09659", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import SystemMessage\n", "from langgraph.prebuilt import ToolNode\n", "\n", "# Step 1: Generate an AIMessage that may include a tool-call to be sent.\n", "def query_or_respond(state: MessagesState):\n", "    \"\"\"Generate tool call for retrieval or respond.\"\"\"\n", "    llm_with_tools = llm.bind_tools([retrieve])\n", "    response = llm_with_tools.invoke(state[\"messages\"])\n", "    # MessagesState appends messages to state instead of overwriting\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# Step 2: Execute the retrieval.\n", "tools = ToolNode([retrieve])\n", "\n", "\n", "# Step 3: Generate a response using the retrieved content.\n", "def generate(state: MessagesState):\n", "    \"\"\"Generate answer.\"\"\"\n", "    # Get generated ToolMessages\n", "    recent_tool_messages = []\n", "    for message in reversed(state[\"messages\"]):\n", "        if message.type == \"tool\":\n", "            recent_tool_messages.append(message)\n", "        else:\n", "            break\n", "    tool_messages = recent_tool_messages[::-1]\n", "\n", "    # Format into prompt\n", "    docs_content = \"\\n\\n\".join(doc.content for doc in tool_messages)\n", "    system_message_content = (\n", "        \"You are an assistant for question-answering tasks. \"\n", "        \"Use the following pieces of retrieved context to answer \"\n", "        \"the question. If you don't know the answer, say that you \"\n", "        \"don't know. Use three sentences maximum and keep the \"\n", "        \"answer concise.\"\n", "        \"\\n\\n\"\n", "        f\"{docs_content}\"\n", "    )\n", "    conversation_messages = [\n", "        message\n", "        for message in state[\"messages\"]\n", "        if message.type in (\"human\", \"system\")\n", "        or (message.type == \"ai\" and not message.tool_calls)\n", "    ]\n", "    prompt = [SystemMessage(system_message_content)] + conversation_messages\n", "\n", "    # Run\n", "    response = llm.invoke(prompt)\n", "    return {\"messages\": [response]}"]}, {"cell_type": "code", "execution_count": null, "id": "a940c627", "metadata": {}, "outputs": [], "source": ["# Compile application and test\n", "graph_builder.add_edge(START, \"retrieve\")\n", "graph = graph_builder.compile()"]}, {"cell_type": "code", "execution_count": 25, "id": "1be9445f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'question': 'What is Task Decomposition?', 'context': [Document(id='8c5793d6-e383-4856-a308-94383c042379', metadata={'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}, page_content='Conversatin samples:\\n[\\n  {\\n    \"role\": \"system\",'), Document(id='aa699447-f9b2-48cd-88a0-43a71a167947', metadata={'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}, page_content='Conversatin samples:\\n[\\n  {\\n    \"role\": \"system\",'), Document(id='50083849-f94a-4cec-a6d0-76b487bff99d', metadata={'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}, page_content='Conversatin samples:\\n[\\n  {\\n    \"role\": \"system\",'), Document(id='f7e05f02-66ae-4f4b-807e-f26089cbb0b1', metadata={'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}, page_content='Conversatin samples:\\n[\\n  {\\n    \"role\": \"system\",')], 'answer': 'I am sorry, but the provided context does not contain information about Task Decomposition. Therefore, I am unable to answer your question.'}\n", "I am sorry, but the provided context does not contain information about Task Decomposition. Therefore, I am unable to answer your question.\n"]}], "source": ["response = graph.invoke({\"question\": \"What is Task Decomposition?\"})\n", "print(response)\n", "print(response[\"answer\"])"]}, {"cell_type": "code", "execution_count": 17, "id": "7f89b417", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['70122918-260d-4a4f-8355-50769058d945', '49622d3f-4ce7-47e8-ba38-4084d4fbfc51', '33053407-e40c-45de-859f-9ed9360e7238']\n"]}], "source": ["document_ids = vector_store.add_documents(documents=all_splits)\n", "\n", "print(document_ids[:3])"]}, {"cell_type": "code", "execution_count": null, "id": "afb34d4e", "metadata": {}, "outputs": [], "source": ["print(docs[0].page_content)"]}, {"cell_type": "code", "execution_count": 12, "id": "d4227aa7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total characters: 43047\n"]}], "source": ["assert len(docs) == 1\n", "print(f\"Total characters: {len(docs[0].page_content)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "9b41cd70", "metadata": {}, "outputs": [], "source": ["print(docs[0].page_content[:500])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}