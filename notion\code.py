# Initialisation
import requests, json
import os
from dotenv import load_dotenv

load_dotenv()

token = os.getenv("NOTION_API_KEY")
databaseID = os.getenv("DATABASE_ID")

headers = {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
    "Notion-Version": "2022-02-22"
}

# Response a Database
def responseDatabase(databaseID,headers):
    readUrl=f"https://api.notion.com/v1/databases/{databaseID}"
    res=requests.request("GET",readUrl,headers=headers)
    print(res.status_code)

def readDatabase(databaseID, headers):
    readUrl = f"https://api.notion.com/v1/databases/{databaseID}/query"
    res = requests.request("POST", readUrl, headers=headers)
    data = res.json()
    print(res.status_code)

    with open('./full-properties.json', 'w', encoding='utf8') as f:
        json.dump(data, f, ensure_ascii=False)
    return data



def filterProjects(filterSchema: dict):
    """ This is help to get a page from a database using the project name """
    readUrl = f"https://api.notion.com/v1/databases/{databaseID}/query"
    res = requests.request("POST", readUrl, headers=headers, data=json.dumps(filterSchema))
    data = res.json()
    return {"status": res.status_code, "data": data}

def getPage(pageId: str):
   updateUrl = f"https://api.notion.com/v1/pages/{pageId}"
   res = requests.request("GET", updateUrl, headers=headers)
   data = res.json()
   return {"status": res.status_code, "data": data}

def createPage(projectName: str, Metadata: dict):
    """ This is help to create a page in a database using the project name """
    createUrl = 'https://api.notion.com/v1/pages'
    
    newPage = {
      "parent": {
        "database_id": {databaseID}
      },
      "properties": {
        "Name": {
          "title": [
            {
              "text": {
                "content": {projectName}
              }
            }
          ]
        },
        "Progress": {
          "status": {
            "name": {Metadata["Progress"]}
          }
        },
        "Stage": {
          "select": {
            "name": {Metadata["Stage"]}
          }
        },
        "Cost": {
          "multi_select": [
            {
              "name": {Metadata["Cost"]}
            }
          ]
        },
        "Potential": {
          "select": {
            "name": {Metadata["Potential"]}
          }
        },
        "Date": {
          "date": {
            "start": {Metadata["StartDate"]}
          }
        }
      }
    }
    
    res = requests.post(createUrl, headers=headers, data=json.dumps(newPage))
    data = res.json()
    return {"status": res.status_code}

def updatePage(pageId: str, Metadata: dict):
    """ This is help to update a page in a database using the page id """
    updateUrl = f"https://api.notion.com/v1/pages/{pageId}"
    res = requests.patch(updateUrl, headers=headers, data=json.dumps(Metadata))
    return {"status": res.status_code}
    
def createPost(pageID: str, content: str):
    """ This is help to create a post in a page using the page id """
    
    createUrl = f"https://api.notion.com/v1/blocks/{pageID}/children"