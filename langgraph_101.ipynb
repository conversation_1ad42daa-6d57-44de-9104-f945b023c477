{"cells": [{"cell_type": "markdown", "id": "dc082433", "metadata": {}, "source": ["# LangGraph 101\n", "\n", "[LLMs](https://python.langchain.com/docs/concepts/chat_models/) make it possible to embed intelligence into a new class of applications. [LangGraph](https://langchain-ai.github.io/langgraph/) is a framework to help build applications with LLMs. Here, we will overview the basics of LangGraph, explain its benefits, show how to use it to build workflows / agents, and show how it works with [<PERSON><PERSON><PERSON><PERSON>](https://www.langchain.com/) / [LangSmith](https://docs.smith.langchain.com/).\n", "\n", "![ecosystem](./img/ecosystem.png)\n", "\n", "## Chat models\n", "\n", "[Chat models](https://python.langchain.com/docs/concepts/chat_models/) are the foundation of LLM applications. They are typically accessed through a chat interface that takes a list of [messages](https://python.langchain.com/docs/concepts/messages/) as input and returns a [message](https://python.langchain.com/docs/concepts/messages/) as output. LangChain provides [a standardized interface for chat models](https://python.langchain.com/api_reference/langchain/chat_models/langchain.chat_models.base.init_chat_model.html), making it easy to [access many different providers](https://python.langchain.com/docs/integrations/chat/)."]}, {"cell_type": "code", "execution_count": 5, "id": "cecc2b24", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "load_dotenv(\".env\", override=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "e0ee8f6c", "metadata": {}, "outputs": [], "source": ["from langchain.chat_models import init_chat_model\n", "llm = init_chat_model(\"gemini-2.0-flash\", model_provider=\"google_genai\")"]}, {"cell_type": "markdown", "id": "50777b0b", "metadata": {}, "source": ["## Running the model\n", "\n", "The `init_chat_model` interface provides [standardized](https://python.langchain.com/docs/concepts/runnables/) methods for using chat models, which include:\n", "- `invoke()`: A single input is transformed into an output.\n", "- `stream()`: Outputs are [streamed](https://python.langchain.com/docs/concepts/streaming/#stream-and-astream) as they are produced. "]}, {"cell_type": "code", "execution_count": null, "id": "a28159d5", "metadata": {}, "outputs": [], "source": ["result = llm.invoke(\"What is an agent?\")"]}, {"cell_type": "code", "execution_count": 8, "id": "41137023", "metadata": {}, "outputs": [{"data": {"text/plain": ["langchain_core.messages.ai.AIMessage"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["type(result)"]}, {"cell_type": "code", "execution_count": 10, "id": "dc1d00eb", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">The term \"agent\" is used in a variety of fields, but generally, it refers to something that can <span style=\"font-weight: bold\">perceive its </span>      \n", "<span style=\"font-weight: bold\">environment and act upon it to achieve a goal.</span> Here's a breakdown of the concept and its different contexts:       \n", "\n", "<span style=\"font-weight: bold\">General Definition:</span>                                                                                                \n", "\n", "At its core, an agent is an entity that:                                                                           \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Perceives:</span> It can sense or receive information about its surroundings. This could be through sensors, inputs, or\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>other means of gathering data.                                                                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Acts:</span> It can take actions or perform operations that affect its environment.                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Goal-Oriented:</span> It has a specific objective or set of objectives it's trying to achieve.                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Autonomous (to some degree):</span> It can make decisions and take actions independently, without constant external    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>control.  The level of autonomy can vary widely.                                                                \n", "\n", "<span style=\"font-weight: bold\">Examples of Agents in Different Contexts:</span>                                                                          \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Artificial Intelligence (AI):</span>                                                                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>An AI agent is a computer program or system designed to act intelligently.                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Examples:                                                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Robots:</span>  Use sensors to perceive the physical world and motors to act.                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Software bots:</span>  Automate tasks online, like web scraping or customer service.                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Virtual assistants:</span>  Like <PERSON><PERSON>, <PERSON><PERSON>, or Google Assistant, respond to voice commands and perform actions.\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Self-driving cars:</span>  Perceive their surroundings through cameras and sensors and control the vehicle.      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Game-playing AI:</span>  Like chess or Go programs, analyze the game state and make moves.                       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Recommender systems:</span>  Suggest products or content based on user preferences.                              \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Economics:</span>                                                                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>An economic agent is an individual, household, firm, or other entity that makes economic decisions.          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Examples:                                                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Consumers:</span>  Make decisions about what to buy.                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Producers:</span>  Make decisions about what to produce and how to produce it.                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Investors:</span>  Make decisions about where to allocate capital.                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Multi-Agent Systems (MAS):</span>                                                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>A MAS is a system composed of multiple interacting agents. These agents can cooperate, compete, or coordinate\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>to achieve a common goal or their individual goals.                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Examples:                                                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Traffic control systems:</span>  Where agents represent individual vehicles or traffic lights.                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Supply chain management systems:</span>  Where agents represent different suppliers, manufacturers, and          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">         </span>distributors.                                                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Robotics swarms:</span>  Where multiple robots work together to perform a task.                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Software Engineering:</span>                                                                                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>A software agent is a piece of software that acts on behalf of a user or another program.  It can be         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>proactive, reactive, and social.                                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Examples:                                                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Monitoring agents:</span>  Track system performance and alert administrators to problems.                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Filtering agents:</span>  Sort and prioritize email or other information.                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Human Agents:</span>                                                                                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>In a legal or business context, an agent is someone authorized to act on behalf of another person (the       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>principal).                                                                                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Examples:                                                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Real estate agents:</span>  Act on behalf of buyers or sellers of property.                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Insurance agents:</span>  Sell insurance policies on behalf of insurance companies.                              \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Literary agents:</span>  Represent authors and negotiate publishing contracts.                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">       • </span><span style=\"font-weight: bold\">Talent agents:</span>  Represent actors, musicians, and other performers.                                        \n", "\n", "<span style=\"font-weight: bold\">Key Characteristics of Agents (in AI and Computer Science):</span>                                                        \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Autonomy:</span> Ability to operate without direct human intervention.                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Reactivity:</span> Ability to perceive the environment and respond to changes.                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Pro-activeness:</span> Ability to take initiative and pursue goals.                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Social ability:</span> Ability to communicate and interact with other agents (in multi-agent systems).                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Learning:</span> Ability to improve performance over time through experience.  (Not all agents have this).             \n", "\n", "<span style=\"font-weight: bold\">In summary, an agent is an entity that can perceive its environment and act upon it to achieve a goal. The specific</span>\n", "<span style=\"font-weight: bold\">characteristics and capabilities of an agent depend on the context in which it is being used.</span>                      \n", "</pre>\n"], "text/plain": ["The term \"agent\" is used in a variety of fields, but generally, it refers to something that can \u001b[1mperceive its \u001b[0m      \n", "\u001b[1menvironment and act upon it to achieve a goal.\u001b[0m Here's a breakdown of the concept and its different contexts:       \n", "\n", "\u001b[1mGeneral Definition:\u001b[0m                                                                                                \n", "\n", "At its core, an agent is an entity that:                                                                           \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mPerceives:\u001b[0m It can sense or receive information about its surroundings. This could be through sensors, inputs, or\n", "\u001b[1;33m   \u001b[0mother means of gathering data.                                                                                  \n", "\u001b[1;33m • \u001b[0m\u001b[1mActs:\u001b[0m It can take actions or perform operations that affect its environment.                                    \n", "\u001b[1;33m • \u001b[0m\u001b[1mGoal-Oriented:\u001b[0m It has a specific objective or set of objectives it's trying to achieve.                         \n", "\u001b[1;33m • \u001b[0m\u001b[1mAutonomous (to some degree):\u001b[0m It can make decisions and take actions independently, without constant external    \n", "\u001b[1;33m   \u001b[0mcontrol.  The level of autonomy can vary widely.                                                                \n", "\n", "\u001b[1mExamples of Agents in Different Contexts:\u001b[0m                                                                          \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mArtificial Intelligence (AI):\u001b[0m                                                                                   \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mAn AI agent is a computer program or system designed to act intelligently.                                   \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mExamples:                                                                                                    \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mRobots:\u001b[0m  Use sensors to perceive the physical world and motors to act.                                    \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mSoftware bots:\u001b[0m  Automate tasks online, like web scraping or customer service.                             \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mVirtual assistants:\u001b[0m  Like <PERSON><PERSON>, <PERSON><PERSON>, or Google Assistant, respond to voice commands and perform actions.\n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mSelf-driving cars:\u001b[0m  Perceive their surroundings through cameras and sensors and control the vehicle.      \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mGame-playing AI:\u001b[0m  Like chess or Go programs, analyze the game state and make moves.                       \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mRecommender systems:\u001b[0m  Suggest products or content based on user preferences.                              \n", "\u001b[1;33m • \u001b[0m\u001b[1mEconomics:\u001b[0m                                                                                                      \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mAn economic agent is an individual, household, firm, or other entity that makes economic decisions.          \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mExamples:                                                                                                    \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mConsumers:\u001b[0m  Make decisions about what to buy.                                                             \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mProducers:\u001b[0m  Make decisions about what to produce and how to produce it.                                   \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mInvestors:\u001b[0m  Make decisions about where to allocate capital.                                               \n", "\u001b[1;33m • \u001b[0m\u001b[1mMulti-Agent Systems (MAS):\u001b[0m                                                                                      \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mA MAS is a system composed of multiple interacting agents. These agents can cooperate, compete, or coordinate\n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0mto achieve a common goal or their individual goals.                                                          \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mExamples:                                                                                                    \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mTraffic control systems:\u001b[0m  Where agents represent individual vehicles or traffic lights.                   \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mSupply chain management systems:\u001b[0m  Where agents represent different suppliers, manufacturers, and          \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0mdistributors.                                                                                             \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mRobotics swarms:\u001b[0m  Where multiple robots work together to perform a task.                                  \n", "\u001b[1;33m • \u001b[0m\u001b[1mSoftware Engineering:\u001b[0m                                                                                           \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mA software agent is a piece of software that acts on behalf of a user or another program.  It can be         \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0mproactive, reactive, and social.                                                                             \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mExamples:                                                                                                    \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mMonitoring agents:\u001b[0m  Track system performance and alert administrators to problems.                        \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mFiltering agents:\u001b[0m  Sort and prioritize email or other information.                                        \n", "\u001b[1;33m • \u001b[0m\u001b[1mHuman Agents:\u001b[0m                                                                                                   \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mIn a legal or business context, an agent is someone authorized to act on behalf of another person (the       \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0mprincipal).                                                                                                  \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mExamples:                                                                                                    \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mReal estate agents:\u001b[0m  Act on behalf of buyers or sellers of property.                                      \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mInsurance agents:\u001b[0m  Sell insurance policies on behalf of insurance companies.                              \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mLiterary agents:\u001b[0m  Represent authors and negotiate publishing contracts.                                   \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m\u001b[1mTalent agents:\u001b[0m  Represent actors, musicians, and other performers.                                        \n", "\n", "\u001b[1mKey Characteristics of Agents (in AI and Computer Science):\u001b[0m                                                        \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mAutonomy:\u001b[0m Ability to operate without direct human intervention.                                                 \n", "\u001b[1;33m • \u001b[0m\u001b[1mReactivity:\u001b[0m Ability to perceive the environment and respond to changes.                                         \n", "\u001b[1;33m • \u001b[0m\u001b[1mPro-activeness:\u001b[0m Ability to take initiative and pursue goals.                                                    \n", "\u001b[1;33m • \u001b[0m\u001b[1mSocial ability:\u001b[0m Ability to communicate and interact with other agents (in multi-agent systems).                 \n", "\u001b[1;33m • \u001b[0m\u001b[1mLearning:\u001b[0m Ability to improve performance over time through experience.  (Not all agents have this).             \n", "\n", "\u001b[1mIn summary, an agent is an entity that can perceive its environment and act upon it to achieve a goal. The specific\u001b[0m\n", "\u001b[1mcharacteristics and capabilities of an agent depend on the context in which it is being used.\u001b[0m                      \n"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from rich.markdown import Markdown\n", "Markdown(result.content)"]}, {"cell_type": "markdown", "id": "9a24d8ef", "metadata": {}, "source": ["## Tools\n", "\n", "[Tools](https://python.langchain.com/docs/concepts/tools/) are utilities that can be called by a chat model. In LangChain, creating tools can be done using the `@tool` decorator, which transforms Python functions into callable tools. It will automatically infer the tool's name, description, and expected arguments from the function definition. You can also use [Model Context Protocol (MCP) servers](https://github.com/langchain-ai/langchain-mcp-adapters) as LangChain-compatible tools. "]}, {"cell_type": "code", "execution_count": 11, "id": "afdff275", "metadata": {}, "outputs": [], "source": ["from langchain.tools import tool\n", "\n", "@tool\n", "def write_email(to: str, subject: str, content: str) -> str:\n", "    \"\"\"Write and send an email.\"\"\"\n", "    # Placeholder response - in real app would send email\n", "    return f\"Email sent to {to} with subject '{subject}' and content: {content}\""]}, {"cell_type": "code", "execution_count": 12, "id": "c52ec55b-0b60-4b0c-95d4-ff528a64694e", "metadata": {}, "outputs": [{"data": {"text/plain": ["langchain_core.tools.structured.StructuredTool"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["type(write_email)"]}, {"cell_type": "code", "execution_count": 13, "id": "23a40647-3d48-4760-aabe-144d627de110", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'to': {'title': 'To', 'type': 'string'},\n", " 'subject': {'title': 'Subject', 'type': 'string'},\n", " 'content': {'title': 'Content', 'type': 'string'}}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["write_email.args"]}, {"cell_type": "code", "execution_count": 14, "id": "abd85ae4-9d4c-4efa-9577-aca96e9f22cd", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Write and send an email.                                                                                           \n", "</pre>\n"], "text/plain": ["Write and send an email.                                                                                           \n"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["Markdown(write_email.description)"]}, {"cell_type": "markdown", "id": "c8a6b427", "metadata": {}, "source": ["## <PERSON><PERSON> Calling\n", "\n", "Tools can be [called](https://python.langchain.com/docs/concepts/tool_calling/) by LLMs. When a tool is bound to the model, the model can choose to call the tool by returning a structured output with tool arguments. We use the `bind_tools` method to augment an LLM with tools.\n", "\n", "![tool-img](img/tool_call_detail.png)\n", "\n", "Providers often have [parameters such as `tool_choice`](https://python.langchain.com/docs/how_to/tool_choice/) to enforce calling specific tools. `any` will select at least one of the tools.\n", "\n", "In addition, we can [set `parallel_tool_calls=False`](https://python.langchain.com/docs/how_to/tool_calling_parallel/) to ensure the model will only call one tool at a time."]}, {"cell_type": "code", "execution_count": 15, "id": "bfa57bc4", "metadata": {}, "outputs": [], "source": ["# Connect tools to a chat model\n", "model_with_tools = llm.bind_tools([write_email], tool_choice=\"any\", parallel_tool_calls=False)\n", "\n", "# The model will now be able to call tools\n", "output = model_with_tools.invoke(\"Draft a response to my boss (<EMAIL>) about tomorrow's meeting\")"]}, {"cell_type": "code", "execution_count": 16, "id": "7985eab6-9e6b-4fa5-8027-52d32886b97e", "metadata": {}, "outputs": [{"data": {"text/plain": ["langchain_core.messages.ai.AIMessage"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["type(output)"]}, {"cell_type": "code", "execution_count": 17, "id": "ea0ce030-e760-4679-838f-d88d1480664e", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={'function_call': {'name': 'write_email', 'arguments': '{\"to\": \"<EMAIL>\", \"content\": \"Hi <PERSON>,\\\\n\\\\nI\\'m looking forward to our meeting tomorrow.\\\\n\\\\nBest,\\\\n[Your Name]\", \"subject\": \"Regarding tomorrow\\'s meeting\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.0-flash', 'safety_ratings': []}, id='run--bda75cb6-3eba-486d-a412-55db10cecf1a-0', tool_calls=[{'name': 'write_email', 'args': {'to': '<EMAIL>', 'content': \"Hi <PERSON>,\\n\\nI'm looking forward to our meeting tomorrow.\\n\\nBest,\\n[Your Name]\", 'subject': \"Regarding tomorrow's meeting\"}, 'id': '3e1fcbf6-5752-4ff2-92d0-0e5530417436', 'type': 'tool_call'}], usage_metadata={'input_tokens': 37, 'output_tokens': 38, 'total_tokens': 75, 'input_token_details': {'cache_read': 0}})"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["output"]}, {"cell_type": "code", "execution_count": 18, "id": "717779cb", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'to': '<EMAIL>',\n", " 'content': \"<PERSON> <PERSON>,\\n\\nI'm looking forward to our meeting tomorrow.\\n\\nBest,\\n[Your Name]\",\n", " 'subject': \"Regarding tomorrow's meeting\"}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Extract tool calls and execute them\n", "args = output.tool_calls[0]['args']\n", "args"]}, {"cell_type": "code", "execution_count": 19, "id": "09f85694", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Email <NAME_EMAIL> with subject 'Regarding tomorrow's meeting' and content: <PERSON>,                    \n", "\n", "I'm looking forward to our meeting tomorrow.                                                                       \n", "\n", "Best, [Your Name]                                                                                                  \n", "</pre>\n"], "text/plain": ["Email <NAME_EMAIL> with subject 'Regarding tomorrow's meeting' and content: <PERSON>,                    \n", "\n", "I'm looking forward to our meeting tomorrow.                                                                       \n", "\n", "Best, [Your Name]                                                                                                  \n"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Call the tool\n", "result = write_email.invoke(args)\n", "Markdown(result)"]}, {"cell_type": "markdown", "id": "b6f9c52a", "metadata": {}, "source": ["![basic_prompt](img/tool_call.png)\n", "\n", "## Workflows\n", " \n", "There are many patterns for building applications with LLMs. \n", "\n", "[We can embed LLM calls into pre-defined workflows](https://langchain-ai.github.io/langgraph/tutorials/workflows/), giving the system more agency to make decisions. \n", "\n", "As an example, we could add a router step to determine whether to write an email or not.\n", "\n", "![workflow_example](img/workflow_example.png)\n", "\n", "## Agents\n", "\n", "We can further increase agency, allowing the LLM to dynamically direct its own tool usage. \n", "\n", "[Agents](https://langchain-ai.github.io/langgraph/tutorials/workflows/) are typically implemented as tool calling in a loop, where the output of each tool call is used to inform the next action.\n", "\n", "![agent_example](img/agent_example.png)\n", "\n", "Agents are well suited to open-ended problems where it's difficult to predict the *exact* steps needed in advance.\n", " \n", "Workflows are often appropriate when the control flow can easily be defined in advance. \n", "\n", "![workflow_v_agent](img/workflow_v_agent.png)\n", "\n", "## What is <PERSON><PERSON><PERSON><PERSON>? \n", "\n", "[LangGraph](https://langchain-ai.github.io/langgraph/concepts/high_level/) provides low-level supporting infrastructure that sits underneath *any* workflow or agent. \n", "\n", "It does not abstract prompts or architecture, and provides a few benefits:\n", "\n", "- **Control**: Make it easy to define and / or combine agents and workflows.\n", "- **Persistence**: Provide a way to persist the state of a graph, which enables both memory and human-in-the-loop.\n", "- **Testing, Debugging, and Deployment**: Provide an easy onramp for testing, debugging, and deploying applications.\n", "\n", "### Control\n", "\n", "LangGraph lets you define your application as a graph with:\n", "\n", "1. *State*: What information do we need to track over the course of the application?\n", "2. *Nodes*: How do we want to update this information over the course of the application?\n", "3. *Edges*: How do we want to connect these nodes together?\n", "\n", "We can use the [`StateGraph` class](https://langchain-ai.github.io/langgraph/concepts/low_level/#graphs) to initialize a LangGraph graph with a [`State` object](https://langchain-ai.github.io/langgraph/concepts/low_level/#state).\n", "\n", "`State` defines the schema for information we want to track over the course of the application. \n", "\n", "This can be any object with `getattr()` in python, such as a dictionary, dataclass, or Pydantic object: \n", "\n", "- TypeDict is fastest but doesn’t support defaults\n", "- Dataclass is basically as fast, supports dot syntax `state.foo`, and has defaults. \n", "- Pydantic is slower (especially with custom validators) but gives type validation."]}, {"cell_type": "code", "execution_count": 20, "id": "3319290a", "metadata": {}, "outputs": [], "source": ["from typing import TypedDict\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "class StateSchema(TypedDict):\n", "    request: str\n", "    email: str\n", "\n", "workflow = StateGraph(StateSchema)"]}, {"cell_type": "markdown", "id": "b84bedb9", "metadata": {}, "source": ["Each node is simply a python function or typescript code. This gives us full control over the logic inside each node.\n", "\n", "They receive the current state, and return a dictionary to update the state.\n", "\n", "By default, [state keys are overwritten](https://langchain-ai.github.io/langgraph/how-tos/state-reducers/). \n", "\n", "However, you can [define custom update logic](https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers). \n", "\n", "![nodes_edges](img/nodes_edges.png)"]}, {"cell_type": "code", "execution_count": 21, "id": "d5e79c1f", "metadata": {}, "outputs": [], "source": ["def write_email_node(state: StateSchema) -> StateSchema:\n", "    # Imperative code that processes the request\n", "    output = model_with_tools.invoke(state[\"request\"])\n", "    args = output.tool_calls[0]['args']\n", "    email = write_email.invoke(args)\n", "    return {\"email\": email}"]}, {"cell_type": "markdown", "id": "737c8040", "metadata": {}, "source": ["Edges connect nodes together. \n", "\n", "We specify the control flow by adding edges and nodes to our state graph. "]}, {"cell_type": "code", "execution_count": 22, "id": "554e0d8b", "metadata": {}, "outputs": [], "source": ["workflow = StateGraph(StateSchema)\n", "workflow.add_node(\"write_email_node\", write_email_node)\n", "workflow.add_edge(START, \"write_email_node\")\n", "workflow.add_edge(\"write_email_node\", END)\n", "\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": 23, "id": "7cc79b40", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'request': \"Draft a response to my boss (<EMAIL>) about tomorrow's meeting\",\n", " 'email': \"<PERSON><PERSON> <NAME_EMAIL> with subject 'Tomorrow's Meeting' and content: <PERSON> <PERSON>,\\n\\nI'm looking forward to our meeting tomorrow.\\n\\nBest,\\n[Your Name]\"}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke({\"request\": \"Draft a response to my boss (<EMAIL>) about tomorrow's meeting\"})"]}, {"cell_type": "markdown", "id": "2446dea9", "metadata": {}, "source": ["Routing between nodes can be done [conditionally](https://langchain-ai.github.io/langgraph/concepts/low_level/#conditional-edges) using a simple function. \n", "\n", "The return value of this function is used as the name of the node (or list of nodes) to send the state to next. \n", "\n", "You can optionally provide a dictionary that maps the `should_continue` output to the name of the next node."]}, {"cell_type": "code", "execution_count": 35, "id": "f29b05bf", "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "from langgraph.graph import MessagesState\n", "# from email_assistant.utils import show_graph\n", "\n", "def call_llm(state: MessagesState) -> MessagesState:\n", "    \"\"\"Run LLM\"\"\"\n", "\n", "    output = model_with_tools.invoke(state[\"messages\"])\n", "    return {\"messages\": [output]}\n", "\n", "def run_tool(state: MessagesState):\n", "    \"\"\"Performs the tool call\"\"\"\n", "\n", "    result = []\n", "    for tool_call in state[\"messages\"][-1].tool_calls:\n", "        observation = write_email.invoke(tool_call[\"args\"])\n", "        result.append({\"role\": \"tool\", \"content\": observation, \"tool_call_id\": tool_call[\"id\"]})\n", "    return {\"messages\": result}\n", "\n", "def should_continue(state: MessagesState) -> Literal[\"run_tool\", \"__end__\"]:\n", "    \"\"\"Route to tool handler, or end if Done tool called\"\"\"\n", "    \n", "    # Get the last message\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    \n", "    # If the last message is a tool call, check if it's a Done tool call\n", "    if last_message.tool_calls:\n", "        return \"run_tool\"\n", "    # Otherwise, we stop (reply to the user)\n", "    return END\n", "\n", "workflow = StateGraph(MessagesState)\n", "workflow.add_node(\"call_llm\", call_llm)\n", "workflow.add_node(\"run_tool\", run_tool)\n", "workflow.add_edge(START, \"call_llm\")\n", "workflow.add_conditional_edges(\"call_llm\", should_continue, {\"run_tool\": \"run_tool\", END: END})\n", "workflow.add_edge(\"run_tool\", END)\n", "\n", "# Run the workflow\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "fd9f07af-c633-4527-b2d9-52c6451dc9c5", "metadata": {}, "outputs": [], "source": ["show_graph(app)"]}, {"cell_type": "code", "execution_count": 36, "id": "<PERSON><PERSON><PERSON><PERSON>", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Draft a response to my boss (<EMAIL>) confirming that I want to attend Interrupt!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  write_email (294ba439-17d2-4c5a-87a5-77178df3e70d)\n", " Call ID: 294ba439-17d2-4c5a-87a5-77178df3e70d\n", "  Args:\n", "    to: <EMAIL>\n", "    content: <PERSON>,\n", "\n", "This email confirms that I will be attending Interrupt!.\n", "\n", "Thanks,\n", "[Your Name]\n", "    subject: Interrupt! Attendance Confirmation\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "Email <NAME_EMAIL> with subject 'Interrupt! Attendance Confirmation' and content: <PERSON> <PERSON>,\n", "\n", "This email confirms that I will be attending Interrupt!.\n", "\n", "Thanks,\n", "[Your Name]\n"]}], "source": ["result = app.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"Draft a response to my boss (<EMAIL>) confirming that I want to attend Interrupt!\"}]})\n", "for m in result[\"messages\"]:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "a78b232d", "metadata": {}, "source": ["With these low level components, you can build many many different workflows and agents. See [this tutorial](https://langchain-ai.github.io/langgraph/tutorials/workflows/)!\n", "\n", "Because agents are such a common pattern, [LangGraph](https://langchain-ai.github.io/langgraph/tutorials/workflows/#pre-built) has [a pre-built agent](https://langchain-ai.github.io/langgraph/agents/overview/?ref=blog.langchain.dev#what-is-an-agent) abstraction.\n", "\n", "With LangGraph's [pre-built method](https://langchain-ai.github.io/langgraph/tutorials/workflows/#pre-built), we just pass in the LLM, tools, and prompt. "]}, {"cell_type": "code", "execution_count": 27, "id": "5a317ad8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Draft a response to my boss (<EMAIL>) confirming that I want to attend Interrupt!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Okay, I can help you with that. What content and subject would you like for the email?\n"]}], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "agent = create_react_agent(\n", "    model=llm,\n", "    tools=[write_email],\n", "    prompt=\"Respond to the user's request using the tools provided.\"  \n", ")\n", "\n", "# Run the agent\n", "result = agent.invoke(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"Draft a response to my boss (<EMAIL>) confirming that I want to attend Interrupt!\"}]}\n", ")\n", "\n", "for m in result[\"messages\"]:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "3c6e506f", "metadata": {}, "source": ["### Persistence\n", "\n", "#### Threads\n", "\n", "It can be very useful to allow agents to pause during long running tasks.\n", "\n", "LangGraph has a built-in persistence layer, implemented through checkpointers, to enable this. \n", "\n", "When you compile graph with a checkpointer, the checkpointer saves a [checkpoint](https://langchain-ai.github.io/langgraph/concepts/persistence/#checkpoints) of the graph state at every step. \n", "\n", "Checkpoints are saved to a thread, which can be accessed after graph execution completes.\n", "\n", "![checkpointer](img/checkpoints.png)\n", "\n", "We compile the graph with a [checkpointer](https://langchain-ai.github.io/langgraph/concepts/persistence/#checkpointer-libraries).\n"]}, {"cell_type": "code", "execution_count": 28, "id": "9a72377e", "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "agent = create_react_agent(\n", "    model=llm,\n", "    tools=[write_email],\n", "    prompt=\"Respond to the user's request using the tools provided.\",\n", "    checkpointer=InMemorySaver()\n", ")\n", "\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "result = agent.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"What are some good practices for writing emails?\"}]}, config)\n", "                    "]}, {"cell_type": "code", "execution_count": 29, "id": "10984007", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What are some good practices for writing emails?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I can help you with that! I can write emails for you, just let me know who the recipient should be, what subject you want, and the content of the email.\n"]}], "source": ["# Get the latest state snapshot\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "state = agent.get_state(config)\n", "for message in state.values['messages']:\n", "    message.pretty_print()"]}, {"cell_type": "code", "execution_count": 30, "id": "7f23ac58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What are some good practices for writing emails?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I can help you with that! I can write emails for you, just let me know who the recipient should be, what subject you want, and the content of the email.\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Good, let's use lesson 3 to craft a response to my boss confirming that I want to attend Interrupt\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Okay, I'm ready. Who is your boss and what is their email address? Also, what is the subject you want for this email? Finally, what content from lesson 3 do you want me to include in the email?\n"]}], "source": ["# Continue the conversation\n", "result = agent.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"Good, let's use lesson 3 to craft a response to my boss confirming that I want to attend Interrupt\"}]}, config)\n", "for m in result['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "code", "execution_count": 31, "id": "5f09fe50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What are some good practices for writing emails?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I can help you with that! I can write emails for you, just let me know who the recipient should be, what subject you want, and the content of the email.\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Good, let's use lesson 3 to craft a response to my boss confirming that I want to attend Interrupt\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Okay, I'm ready. Who is your boss and what is their email address? Also, what is the subject you want for this email? Finally, what content from lesson 3 do you want me to include in the email?\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "I like this, let's write the <NAME_EMAIL>\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Great. What subject line do you want to use? Also, please provide the content of the email.\n"]}], "source": ["# Continue the conversation\n", "result = agent.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"I like this, let's write the <NAME_EMAIL>\"}]}, config)\n", "for m in result['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "4ae8a5d2-cf8a-4465-8d1b-96bb6c6276cf", "metadata": {}, "source": ["#### Interrupts\n", "\n", "In LangGraph, we can also use [interrupts](https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/wait-user-input/) to stop graph execution at specific points.\n", "\n", "Often this is used to collect input from a user and continue execution with collected input."]}, {"cell_type": "code", "execution_count": 32, "id": "52c17b60-9474-49a5-b4a1-583b0dc8bba7", "metadata": {}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "from langgraph.types import Command, interrupt\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "class State(TypedDict):\n", "    input: str\n", "    user_feedback: str\n", "\n", "def step_1(state):\n", "    print(\"---Step 1---\")\n", "    pass\n", "\n", "def human_feedback(state):\n", "    print(\"---human_feedback---\")\n", "    feedback = interrupt(\"Please provide feedback:\")\n", "    return {\"user_feedback\": feedback}\n", "\n", "def step_3(state):\n", "    print(\"---Step 3---\")\n", "    pass\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(\"step_1\", step_1)\n", "builder.add_node(\"human_feedback\", human_feedback)\n", "builder.add_node(\"step_3\", step_3)\n", "builder.add_edge(START, \"step_1\")\n", "builder.add_edge(\"step_1\", \"human_feedback\")\n", "builder.add_edge(\"human_feedback\", \"step_3\")\n", "builder.add_edge(\"step_3\", END)\n", "\n", "# Set up memory\n", "memory = InMemorySaver()\n", "\n", "# Add\n", "graph = builder.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": null, "id": "57c94cb7-067c-4bc5-be33-b615d8e5775e", "metadata": {"scrolled": true}, "outputs": [], "source": ["show_graph(graph)"]}, {"cell_type": "code", "execution_count": 33, "id": "028372b5-88ae-4f13-813d-22430a697f07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Step 1---\n", "{'step_1': None}\n", "\n", "\n", "---human_feedback---\n", "{'__interrupt__': (Interrupt(value='Please provide feedback:', resumable=True, ns=['human_feedback:0ac0022c-e0a2-2e7e-2865-4550041f75b4']),)}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"input\": \"hello world\"}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "f142d467-7b4c-4a04-bad5-bf3fbe953b84", "metadata": {}, "source": ["To resume from an interrupt, we can use [the `Command` object](https://langchain-ai.github.io/langgraph/how-tos/command/). \n", "\n", "We'll use it to resume the graph from the interrupted state, passing the value to return from the interrupt call to `resume`. "]}, {"cell_type": "code", "execution_count": 34, "id": "d90374d3-65a1-4658-82ee-a16cc2835cf4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---human_feedback---\n", "{'human_feedback': {'user_feedback': 'go to step 3!'}}\n", "\n", "\n", "---Step 3---\n", "{'step_3': None}\n", "\n", "\n"]}], "source": ["# Continue the graph execution\n", "for event in graph.stream(\n", "    Command(resume=\"go to step 3!\"),\n", "    thread,\n", "    stream_mode=\"updates\",\n", "):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "8639a518", "metadata": {}, "source": ["### Tracing\n", "\n", "When we are using LangChain or LangGraph, LangSmith logging [will work out of the box](https://docs.smith.langchain.com/observability/how_to_guides/trace_with_langgraph) with the following environment variables set:\n", "\n", "```\n", "export LANGSMITH_TRACING=true\n", "export LANGSMITH_API_KEY=\"<your-langsmith-api-key>\"\n", "```"]}, {"cell_type": "markdown", "id": "9cb2a3e5", "metadata": {}, "source": ["Here is the <PERSON><PERSON><PERSON> trace from above agent execution:\n", "\n", "https://smith.langchain.com/public/6f77014f-d054-44ed-aa2c-8b06ceab689f/r\n", "\n", "We can see that the agent is able to continue the conversation from the previous state because we used a checkpointer."]}, {"cell_type": "markdown", "id": "f0269214", "metadata": {}, "source": ["### Deployment\n", "\n", "We can also deploy our graph using [LangGraph Platform](https://langchain-ai.github.io/langgraph/concepts/langgraph_platform/). \n", "\n", "This creates a server [with an API](https://langchain-ai.github.io/langgraph/cloud/reference/api/api_ref.html) that we can use to interact with our graph and an interactive IDE, LangGraph [Studio](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/).\n", "\n", "We simply need to ensure our project has [a structure](https://langchain-ai.github.io/langgraph/concepts/application_structure/) like this:\n", "\n", "```\n", "my-app/\n", "├── src/email_assistant # all project code lies within here\n", "│   └── langgraph101.py # code for constructing your graph\n", "├── .env # environment variables\n", "├── langgraph.json  # configuration file for LangGraph\n", "└── pyproject.toml # dependencies for your project\n", "```\n", "\n", "The `langgraph.json` file specifies the dependencies, graphs, environment variables, and other settings required to start a LangGraph server.\n", "\n", "To test this, let's deploy `langgraph_101.py`. We have it in our `langgraph.json` file in this repo:\n", "\n", "```\n", " \"langgraph101\": \"./src/email_assistant/langgraph_101.py:app\",\n", "```\n", "\n", "For LangGraph Platform, there are a range of [deployment options](https://langchain-ai.github.io/langgraph/tutorials/deployment/): \n", " \n", "* Local deployments can be started with `langgraph dev` from the root directory of the repo. Checkpoints are saved to the local filesystem.\n", "* There are also various [self-hosted options](https://langchain-ai.github.io/langgraph/tutorials/deployment/#other-deployment-options). \n", "* For hosted deployments, checkpoints are saved to Postgres using a postgres [checkpointer](https://langchain-ai.github.io/langgraph/concepts/persistence/#checkpointer-libraries). \n", "\n", "Test: \n", "```\n", "Draft a response to my boss (<EMAIL>) confirming that I want to attent Interrupt!\n", "```"]}, {"cell_type": "markdown", "id": "f3644093", "metadata": {}, "source": ["Here we can see a visualization of the graph as well as the graph state in Studio.\n", "\n", "![langgraph_studio](img/langgraph_studio.png)\n", "\n", "Also, you can see API docs for the local deployment here:\n", "\n", "http://127.0.0.1:2024/docs"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "main_language": "python", "notebook_metadata_filter": "-all"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}