{"cells": [{"cell_type": "markdown", "id": "7bb66df4", "metadata": {}, "source": ["# Building Agents \n", " \n", "> Note: Optionally, see [these slides](https://docs.google.com/presentation/d/13c0L1CQWAL7fuCXakOqjkvoodfynPJI4Hw_4H76okVU/edit?usp=sharing) and [langgraph_101.ipynb](langgraph_101.ipynb) for context before diving into this notebook!\n", "\n", "We're going to build an email assistant from scratch, starting here with 1) the agent architecture (using [LangGraph](https://langchain-ai.github.io/langgraph/)) and following with 2) testing (using [Lang<PERSON>mith](https://docs.smith.langchain.com/)), 3) human-in-the-loop, and 4) memory. This diagram show how these pieces will fit together:\n", "\n", "![overview-img](img/overview.png)"]}, {"cell_type": "markdown", "id": "19d34429", "metadata": {}, "source": ["#### Load environment variables"]}, {"cell_type": "code", "execution_count": 31, "id": "46c9f78e", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "load_dotenv(\"../.env\")"]}, {"cell_type": "markdown", "id": "54a69e9a", "metadata": {}, "source": ["## Tool Definition\n", "\n", "Let's start by defining some simple tools that an email assistant will use with the `@tool` decorator:"]}, {"cell_type": "code", "execution_count": 32, "id": "f2b708ec", "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "from datetime import datetime\n", "from pydantic import BaseModel\n", "from langchain_core.tools import tool\n", "\n", "@tool\n", "def write_email(to: str, subject: str, content: str) -> str:\n", "    \"\"\"Write and send an email.\"\"\"\n", "    # Placeholder response - in real app would send email\n", "    return f\"Email sent to {to} with subject '{subject}' and content: {content}\"\n", "\n", "@tool\n", "def schedule_meeting(\n", "    attendees: list[str], subject: str, duration_minutes: int, preferred_day: datetime, start_time: int\n", ") -> str:\n", "    \"\"\"Schedule a calendar meeting.\"\"\"\n", "    # Placeholder response - in real app would check calendar and schedule\n", "    date_str = preferred_day.strftime(\"%A, %B %d, %Y\")\n", "    return f\"Meeting '{subject}' scheduled on {date_str} at {start_time} for {duration_minutes} minutes with {len(attendees)} attendees\"\n", "\n", "@tool\n", "def check_calendar_availability(day: str) -> str:\n", "    \"\"\"Check calendar availability for a given day.\"\"\"\n", "    # Placeholder response - in real app would check actual calendar\n", "    return f\"Available times on {day}: 9:00 AM, 2:00 PM, 4:00 PM\"\n", "\n", "@tool\n", "class Done(BaseModel):\n", "      \"\"\"E-mail has been sent.\"\"\"\n", "      done: bool"]}, {"cell_type": "markdown", "id": "2911c929-5c41-4dcd-9cc8-21a8ff82b769", "metadata": {}, "source": ["## Building our email assistant\n", "\n", "We'll combine a [router and agent](https://langchain-ai.github.io/langgraph/tutorials/workflows/) to build our email assistant.\n", "\n", "![agent_workflow_img](img/email_workflow.png)\n", "\n", "### Router\n", "\n", "The routing step handles the triage decision. \n", "\n", "The triage router only focuses on the triage decision, while the agent focuses *only* on the response. \n", "\n", "#### State\n", "\n", "When building an agent, it's important to consider the information that you want to track over time. We'll use LangGraph's pre-built [`MessagesState` object](https://langchain-ai.github.io/langgraph/concepts/low_level/#messagesstate), which is a just dictionary with a `messages` key that appends messages returned by nodes [as its update logic](https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers). However, LangGraph gives you flexibility to track other information. We'll define a custom `State` object that extends `MessagesState` and adds a `classification_decision` key:"]}, {"cell_type": "code", "execution_count": 33, "id": "692537ec-f09e-4086-81e4-9c517273b854", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import MessagesState\n", "\n", "class State(MessagesState):\n", "    # We can add a specific key to our state for the email input\n", "    email_input: dict\n", "    classification_decision: Literal[\"ignore\", \"respond\", \"notify\"]"]}, {"cell_type": "markdown", "id": "d6cd1647-6d58-4aae-b954-6a9c5790c20c", "metadata": {}, "source": ["#### Triage node\n", "\n", "We define a python function with our triage routing logic.\n", "\n", "For this, we use [structured outputs](https://python.langchain.com/docs/concepts/structured_outputs/) with a Pydantic model, which is particularly useful for defining structured output schemas because it offers type hints and validation. The descriptions in the pydantic model are important because they get passed as part JSON schema to the LLM to inform the output coercion."]}, {"cell_type": "code", "execution_count": 61, "id": "8adf520b-adf5-4a7b-b7a8-b8c23720c03f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "from pydantic import BaseModel, Field\n", "from email_assistant.utils import parse_email, format_email_markdown\n", "from email_assistant.prompts import triage_system_prompt, triage_user_prompt, default_triage_instructions, default_background\n", "from langchain.chat_models import init_chat_model\n", "from langgraph.graph import END\n", "from langgraph.types import Command"]}, {"cell_type": "code", "execution_count": 35, "id": "2c2c2ff0-da93-4731-b5b6-0ccd59e0e783", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">&lt; Role &gt; Your role is to triage incoming emails based upon instructs and background information below. &lt;/ Role &gt;   \n", "\n", "&lt; Background &gt; {background}. &lt;/ Background &gt;                                                                       \n", "\n", "&lt; Instructions &gt; Categorize each email into one of three categories:                                               \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 1 </span>IGNORE - Emails that are not worth responding to or tracking                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 2 </span>NOTIFY - Important information that worth notification but doesn't require a response                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 3 </span>RESPOND - Emails that need a direct response Classify the below email into one of these categories. &lt;/          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>Instructions &gt;                                                                                                  \n", "\n", "&lt; Rules &gt; {triage_instructions} &lt;/ Rules &gt;                                                                         \n", "</pre>\n"], "text/plain": ["< Role > Your role is to triage incoming emails based upon instructs and background information below. </ Role >   \n", "\n", "< Background > {background}. </ Background >                                                                       \n", "\n", "< Instructions > Categorize each email into one of three categories:                                               \n", "\n", "\u001b[1;33m 1 \u001b[0mIGNORE - Emails that are not worth responding to or tracking                                                    \n", "\u001b[1;33m 2 \u001b[0mNOTIFY - Important information that worth notification but doesn't require a response                           \n", "\u001b[1;33m 3 \u001b[0mRESPOND - Emails that need a direct response Classify the below email into one of these categories. </          \n", "\u001b[1;33m   \u001b[0mInstructions >                                                                                                  \n", "\n", "< Rules > {triage_instructions} </ Rules >                                                                         \n"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["from rich.markdown import Markdown\n", "Markdown(triage_system_prompt)"]}, {"cell_type": "code", "execution_count": 36, "id": "f3a1ad2c-40a2-42d0-a4b8-7a25df825fad", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Please determine how to handle the below email thread:                                                             \n", "\n", "From: {author} To: {to} Subject: {subject} {email_thread}                                                          \n", "</pre>\n"], "text/plain": ["Please determine how to handle the below email thread:                                                             \n", "\n", "From: {author} To: {to} Subject: {subject} {email_thread}                                                          \n"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["Markdown(triage_user_prompt)"]}, {"cell_type": "code", "execution_count": 37, "id": "69b0df31-b9d2-423f-ba07-67eb0643c2ba", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">I'm <PERSON>, a software engineer at LangChain.                                                                       \n", "</pre>\n"], "text/plain": ["I'm <PERSON>, a software engineer at LangChain.                                                                       \n"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["Markdown(default_background)"]}, {"cell_type": "code", "execution_count": 38, "id": "4b3ea767-6ac1-4562-8ca6-5fa451495786", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Emails that are not worth responding to:                                                                           \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Marketing newsletters and promotional emails                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Spam or suspicious emails                                                                                       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>CC'd on FYI threads with no direct questions                                                                    \n", "\n", "There are also other things that should be known about, but don't require an email response. For these, you should \n", "notify (using the <span style=\"color: #008080; text-decoration-color: #008080; background-color: #000000; font-weight: bold\">notify</span> response). Examples of this include:                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Team member out sick or on vacation                                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Build system notifications or deployments                                                                       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Project status updates without action items                                                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Important company announcements                                                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>FYI emails that contain relevant information for current projects                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>HR Department deadline reminders                                                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Subscription status / renewal reminders                                                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>GitHub notifications                                                                                            \n", "\n", "Emails that are worth responding to:                                                                               \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Direct questions from team members requiring expertise                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Meeting requests requiring confirmation                                                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Critical bug reports related to team's projects                                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Requests from management requiring acknowledgment                                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Client inquiries about project status or features                                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Technical questions about documentation, code, or APIs (especially questions about missing endpoints or         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>features)                                                                                                       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Personal reminders related to family (wife / daughter)                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Personal reminder related to self-care (doctor appointments, etc)                                               \n", "</pre>\n"], "text/plain": ["Emails that are not worth responding to:                                                                           \n", "\n", "\u001b[1;33m • \u001b[0mMarketing newsletters and promotional emails                                                                    \n", "\u001b[1;33m • \u001b[0mSpam or suspicious emails                                                                                       \n", "\u001b[1;33m • \u001b[0mCC'd on FYI threads with no direct questions                                                                    \n", "\n", "There are also other things that should be known about, but don't require an email response. For these, you should \n", "notify (using the \u001b[1;36;40mnotify\u001b[0m response). Examples of this include:                                                      \n", "\n", "\u001b[1;33m • \u001b[0mTeam member out sick or on vacation                                                                             \n", "\u001b[1;33m • \u001b[0mBuild system notifications or deployments                                                                       \n", "\u001b[1;33m • \u001b[0mProject status updates without action items                                                                     \n", "\u001b[1;33m • \u001b[0mImportant company announcements                                                                                 \n", "\u001b[1;33m • \u001b[0mFYI emails that contain relevant information for current projects                                               \n", "\u001b[1;33m • \u001b[0mHR Department deadline reminders                                                                                \n", "\u001b[1;33m • \u001b[0mSubscription status / renewal reminders                                                                         \n", "\u001b[1;33m • \u001b[0mGitHub notifications                                                                                            \n", "\n", "Emails that are worth responding to:                                                                               \n", "\n", "\u001b[1;33m • \u001b[0mDirect questions from team members requiring expertise                                                          \n", "\u001b[1;33m • \u001b[0mMeeting requests requiring confirmation                                                                         \n", "\u001b[1;33m • \u001b[0mCritical bug reports related to team's projects                                                                 \n", "\u001b[1;33m • \u001b[0mRequests from management requiring acknowledgment                                                               \n", "\u001b[1;33m • \u001b[0mClient inquiries about project status or features                                                               \n", "\u001b[1;33m • \u001b[0mTechnical questions about documentation, code, or APIs (especially questions about missing endpoints or         \n", "\u001b[1;33m   \u001b[0mfeatures)                                                                                                       \n", "\u001b[1;33m • \u001b[0mPersonal reminders related to family (wife / daughter)                                                          \n", "\u001b[1;33m • \u001b[0mPersonal reminder related to self-care (doctor appointments, etc)                                               \n"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["Markdown(default_triage_instructions)"]}, {"cell_type": "code", "execution_count": null, "id": "c54ae6a6-94d9-4160-8d45-18f4d29aa600", "metadata": {}, "outputs": [], "source": ["class RouterSchema(BaseModel):\n", "    \"\"\"Analyze the unread email and route it according to its content.\"\"\"\n", "\n", "    reasoning: str = Field(\n", "        description=\"Step-by-step reasoning behind the classification.\"\n", "    )\n", "    classification: Literal[\"ignore\", \"respond\", \"notify\"] = Field(\n", "        description=\"The classification of an email: 'ignore' for irrelevant emails, \"\n", "        \"'notify' for important information that doesn't need a response, \"\n", "        \"'respond' for emails that need a reply\",\n", "    )\n", "\n", "# Initialize the LLM for use with router / structured output\n", "llm = init_chat_model(\"gemini-2.5-pro\", model_provider=\"google_genai\")\n", "llm_router = llm.with_structured_output(RouterSchema) \n", "\n", "def triage_router(state: State) -> Command[Literal[\"response_agent\", \"__end__\"]]:\n", "    \"\"\"Analyze email content to decide if we should respond, notify, or ignore.\"\"\"\n", "    \n", "    author, to, subject, email_thread = parse_email(state[\"email_input\"])\n", "    system_prompt = triage_system_prompt.format(\n", "        background=default_background,\n", "        triage_instructions=default_triage_instructions\n", "    )\n", "\n", "    user_prompt = triage_user_prompt.format(\n", "        author=author, to=to, subject=subject, email_thread=email_thread\n", "    )\n", "\n", "    result = llm_router.invoke(\n", "        [\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt},\n", "        ]\n", "    )\n", "    \n", "    if result.classification == \"respond\":\n", "        print(\"📧 Classification: RESPOND - This email requires a response\")\n", "        goto = \"response_agent\"\n", "        update = {\n", "            \"messages\": [\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": f\"Respond to the email: \\n\\n{format_email_markdown(subject, author, to, email_thread)}\",\n", "                }\n", "            ],\n", "            \"classification_decision\": result.classification,\n", "        }\n", "        \n", "    elif result.classification == \"ignore\":\n", "        print(\"🚫 Classification: IGNORE - This email can be safely ignored\")\n", "        goto = END\n", "        update =  {\n", "            \"classification_decision\": result.classification,\n", "        }\n", "        \n", "    elif result.classification == \"notify\":\n", "        print(\"🔔 Classification: NOTIFY - This email contains important information\")\n", "        # For now, we go to END. But we will add to this later!\n", "        goto = END\n", "        update = {\n", "            \"classification_decision\": result.classification,\n", "        }\n", "        \n", "    else:\n", "        raise ValueError(f\"Invalid classification: {result.classification}\")\n", "    return Command(goto=goto, update=update)"]}, {"cell_type": "markdown", "id": "272d8715", "metadata": {}, "source": ["We use [Command](https://langchain-ai.github.io/langgraph/how-tos/command/) objects in LangGraph to both update the state and select the next node to visit. This is a useful alternative to edges.\n", "\n", "### Agent\n", "\n", "Now, let's build the agent.\n", "\n", "#### LLM node\n", "\n", "Here, we define the LLM decision-making node. This node takes in the current state, calls the LLM, and updates `messages` with the LLM output. \n", "\n", "We [enforce tool use with OpenAI](https://python.langchain.com/docs/how_to/tool_choice/) by setting `tool_choice=\"required\"`."]}, {"cell_type": "code", "execution_count": 40, "id": "1e842b3c-06f5-440f-8159-995503ef3a99", "metadata": {}, "outputs": [], "source": ["from email_assistant.tools.default.prompt_templates import AGENT_TOOLS_PROMPT\n", "from email_assistant.prompts import agent_system_prompt, default_response_preferences, default_cal_preferences"]}, {"cell_type": "code", "execution_count": 41, "id": "8f69c6fc-70aa-48f1-8312-2b1818469a1b", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 1 </span>write_email(to, subject, content) - Send emails to specified recipients                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 2 </span>schedule_meeting(attendees, subject, duration_minutes, preferred_day, start_time) - Schedule calendar meetings  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>where preferred_day is a datetime object                                                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 3 </span>check_calendar_availability(day) - Check available time slots for a given day                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 4 </span>Done - E-mail has been sent                                                                                     \n", "</pre>\n"], "text/plain": ["\n", "\u001b[1;33m 1 \u001b[0mwrite_email(to, subject, content) - Send emails to specified recipients                                         \n", "\u001b[1;33m 2 \u001b[0mschedule_meeting(attendees, subject, duration_minutes, preferred_day, start_time) - Schedule calendar meetings  \n", "\u001b[1;33m   \u001b[0mwhere preferred_day is a datetime object                                                                        \n", "\u001b[1;33m 3 \u001b[0mcheck_calendar_availability(day) - Check available time slots for a given day                                   \n", "\u001b[1;33m 4 \u001b[0mDone - E-mail has been sent                                                                                     \n"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["Markdown(AGENT_TOOLS_PROMPT)"]}, {"cell_type": "code", "execution_count": 42, "id": "9052fced-3fdb-4cd2-ac88-e2ccdce14e7c", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">&lt; Role &gt; You are a top-notch executive assistant who cares about helping your executive perform as well as         \n", "possible. &lt;/ Role &gt;                                                                                                \n", "\n", "&lt; Tools &gt; You have access to the following tools to help manage communications and schedule: {tools_prompt} &lt;/     \n", "Tools &gt;                                                                                                            \n", "\n", "&lt; Instructions &gt; When handling emails, follow these steps:                                                         \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 1 </span>Carefully analyze the email content and purpose                                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 2 </span>IMPORTANT --- always call a tool and call one tool at a time until the task is complete:                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 3 </span>For responding to the email, draft a response email with the write_email tool                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 4 </span>For meeting requests, use the check_calendar_availability tool to find open time slots                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 5 </span>To schedule a meeting, use the schedule_meeting tool with a datetime object for the preferred_day parameter     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Today's date is 2025-07-30 - use this for scheduling meetings accurately                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 6 </span>If you scheduled a meeting, then draft a short response email using the write_email tool                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 7 </span>After using the write_email tool, the task is complete                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 8 </span>If you have sent the email, then use the Done tool to indicate that the task is complete &lt;/ Instructions &gt;      \n", "\n", "&lt; Background &gt; {background} &lt;/ Background &gt;                                                                        \n", "\n", "&lt; Response Preferences &gt; {response_preferences} &lt;/ Response Preferences &gt;                                          \n", "\n", "&lt; Calendar Preferences &gt; {cal_preferences} &lt;/ Calendar Preferences &gt;                                               \n", "</pre>\n"], "text/plain": ["< Role > You are a top-notch executive assistant who cares about helping your executive perform as well as         \n", "possible. </ Role >                                                                                                \n", "\n", "< Tools > You have access to the following tools to help manage communications and schedule: {tools_prompt} </     \n", "Tools >                                                                                                            \n", "\n", "< Instructions > When handling emails, follow these steps:                                                         \n", "\n", "\u001b[1;33m 1 \u001b[0mCarefully analyze the email content and purpose                                                                 \n", "\u001b[1;33m 2 \u001b[0mIMPORTANT --- always call a tool and call one tool at a time until the task is complete:                        \n", "\u001b[1;33m 3 \u001b[0mFor responding to the email, draft a response email with the write_email tool                                   \n", "\u001b[1;33m 4 \u001b[0mFor meeting requests, use the check_calendar_availability tool to find open time slots                          \n", "\u001b[1;33m 5 \u001b[0mTo schedule a meeting, use the schedule_meeting tool with a datetime object for the preferred_day parameter     \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m<PERSON><PERSON><PERSON>'s date is 2025-07-30 - use this for scheduling meetings accurately                                     \n", "\u001b[1;33m 6 \u001b[0mIf you scheduled a meeting, then draft a short response email using the write_email tool                        \n", "\u001b[1;33m 7 \u001b[0mAfter using the write_email tool, the task is complete                                                          \n", "\u001b[1;33m 8 \u001b[0mIf you have sent the email, then use the Done tool to indicate that the task is complete </ Instructions >      \n", "\n", "< Background > {background} </ Background >                                                                        \n", "\n", "< Response Preferences > {response_preferences} </ Response Preferences >                                          \n", "\n", "< Calendar Preferences > {cal_preferences} </ Calendar Preferences >                                               \n"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["Markdown(agent_system_prompt)"]}, {"cell_type": "code", "execution_count": 43, "id": "6f2c120f", "metadata": {}, "outputs": [], "source": ["# Collect all tools\n", "tools = [write_email, schedule_meeting, check_calendar_availability, Done]\n", "tools_by_name = {tool.name: tool for tool in tools}\n", "\n", "# Initialize the LLM, enforcing tool use\n", "# llm = init_chat_model(\"openai:gpt-4.1\", temperature=0.0)\n", "llm_with_tools = llm.bind_tools(tools, tool_choice=\"any\")\n", "\n", "def llm_call(state: State):\n", "    \"\"\"LLM decides whether to call a tool or not\"\"\"\n", "    print(\"LLM_call is calling\")\n", "    return {\n", "        \"messages\": [\n", "            # Invoke the LLM\n", "            llm_with_tools.invoke(\n", "                # Add the system prompt\n", "                [   \n", "                    {\"role\": \"system\", \"content\": agent_system_prompt.format(\n", "                        tools_prompt=AGENT_TOOLS_PROMPT,\n", "                        background=default_background,\n", "                        response_preferences=default_response_preferences,\n", "                        cal_preferences=default_cal_preferences, \n", "                    )}\n", "                ]\n", "                # Add the current messages to the prompt\n", "                + state[\"messages\"]\n", "            )\n", "        ]\n", "    }"]}, {"cell_type": "markdown", "id": "9f05d11a", "metadata": {}, "source": ["#### Tool handler node\n", "\n", "After the LLM makes a decision, we need to execute the chosen tool. \n", "\n", "The `tool_handler` node executes the tool. We can see that nodes can update the graph state to capture any important state changes, such as the classification decision."]}, {"cell_type": "code", "execution_count": 44, "id": "43eb6dc2", "metadata": {}, "outputs": [], "source": ["def tool_handler(state: State):\n", "    \"\"\"Performs the tool call.\"\"\"\n", "\n", "    # List for tool messages\n", "    result = []\n", "    print(\"<PERSON><PERSON><PERSON><PERSON><PERSON> is calling\")\n", "    \n", "    # Iterate through tool calls\n", "    for tool_call in state[\"messages\"][-1].tool_calls:\n", "        # Get the tool\n", "        tool = tools_by_name[tool_call[\"name\"]]\n", "        # Run it\n", "        observation = tool.invoke(tool_call[\"args\"])\n", "        # Create a tool message\n", "        result.append({\"role\": \"tool\", \"content\" : observation, \"tool_call_id\": tool_call[\"id\"]})\n", "    \n", "    # Add it to our messages\n", "    return {\"messages\": result}"]}, {"cell_type": "markdown", "id": "4721<PERSON>e", "metadata": {}, "source": ["#### Conditional Routing\n", "\n", "Our agent needs to decide when to continue using tools and when to stop. This conditional routing function directs the agent to either continue or terminate."]}, {"cell_type": "code", "execution_count": 45, "id": "7c7cbea7", "metadata": {}, "outputs": [], "source": ["def should_continue(state: State) -> Literal[\"tool_handler\", \"__end__\"]:\n", "    \"\"\"Route to tool handler, or end if Done tool called.\"\"\"\n", "    \n", "    # Get the last message\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    print(\"Should continue is calling\")\n", "    # Check if it's a Done tool call\n", "    if last_message.tool_calls:\n", "        for tool_call in last_message.tool_calls: \n", "            if tool_call[\"name\"] == \"Done\":\n", "                return END\n", "            else:\n", "                return \"tool_handler\""]}, {"cell_type": "markdown", "id": "6eb4ede8", "metadata": {}, "source": ["#### Agent Graph\n", "\n", "Finally, we can assemble all components:"]}, {"cell_type": "code", "execution_count": 46, "id": "f81df767", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from email_assistant.utils import show_graph\n", "\n", "# Build workflow\n", "overall_workflow = StateGraph(State)\n", "\n", "# Add nodes\n", "overall_workflow.add_node(\"llm_call\", llm_call)\n", "overall_workflow.add_node(\"tool_handler\", tool_handler)\n", "\n", "# Add edges\n", "overall_workflow.add_edge(START, \"llm_call\")\n", "overall_workflow.add_conditional_edges(\n", "    \"llm_call\",\n", "    should_continue,\n", "    {\n", "        \"tool_handler\": \"tool_handler\",\n", "        END: END,\n", "    },\n", ")\n", "overall_workflow.add_edge(\"tool_handler\", \"llm_call\")\n", "\n", "# Compile the agent\n", "agent = overall_workflow.compile()"]}, {"cell_type": "code", "execution_count": 47, "id": "617f6373-bf48-44c2-ba33-000c9f22b067", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["# View\n", "show_graph(agent)"]}, {"cell_type": "code", "execution_count": 48, "id": "63afbc9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LLM_call is calling\n", "Should continue is calling\n", "<PERSON><PERSON><PERSON><PERSON><PERSON> is calling\n", "LLM_call is calling\n", "Should continue is calling\n"]}], "source": ["output = agent.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"hello I want to send <NAME_EMAIL> and the conetnet is 'What in the time you leave from home\"}]})"]}, {"cell_type": "code", "execution_count": 49, "id": "83357a34", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"hello I want to send <NAME_EMAIL> and the conetnet is 'What in the time you leave from home\", additional_kwargs={}, response_metadata={}, id='b622f07a-b2f3-4364-b95b-0ec81f85a7a1'),\n", "  AIMessage(content='', additional_kwargs={'function_call': {'name': 'write_email', 'arguments': '{\"to\": \"<EMAIL>\", \"content\": \"What in the time you leave from home\", \"subject\": \"Can we meet?\"}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--13fbae2f-3a17-475e-ba3a-500fa0aa1aa4-0', tool_calls=[{'name': 'write_email', 'args': {'to': '<EMAIL>', 'content': 'What in the time you leave from home', 'subject': 'Can we meet?'}, 'id': '1c0c1c78-30a8-4547-9269-88e2c42b83f3', 'type': 'tool_call'}], usage_metadata={'input_tokens': 973, 'output_tokens': 143, 'total_tokens': 1116, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 104}}),\n", "  ToolMessage(content=\"Email <NAME_EMAIL> with subject 'Can we meet?' and content: What in the time you leave from home\", id='e7d13e72-176f-4e56-b1a7-32e1ed43ea0e', tool_call_id='1c0c1c78-30a8-4547-9269-88e2c42b83f3'),\n", "  AIMessage(content='', additional_kwargs={'function_call': {'name': 'Done', 'arguments': '{\"done\": true}'}}, response_metadata={'prompt_feedback': {'block_reason': 0, 'safety_ratings': []}, 'finish_reason': 'STOP', 'model_name': 'gemini-2.5-pro', 'safety_ratings': []}, id='run--40c3f73a-faac-4253-b70c-07af7f193754-0', tool_calls=[{'name': 'Done', 'args': {'done': True}, 'id': '49b115da-4c7d-4625-a16d-597277aeb6e4', 'type': 'tool_call'}], usage_metadata={'input_tokens': 1054, 'output_tokens': 137, 'total_tokens': 1191, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 125}})]}"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["output"]}, {"cell_type": "code", "execution_count": 50, "id": "c7290d1f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hello I want to send <NAME_EMAIL> and the conetnet is 'What in the time you leave from home\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  write_email (1c0c1c78-30a8-4547-9269-88e2c42b83f3)\n", " Call ID: 1c0c1c78-30a8-4547-9269-88e2c42b83f3\n", "  Args:\n", "    to: <EMAIL>\n", "    content: What in the time you leave from home\n", "    subject: Can we meet?\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "Email <NAME_EMAIL> with subject 'Can we meet?' and content: What in the time you leave from home\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Done (49b115da-4c7d-4625-a16d-597277aeb6e4)\n", " Call ID: 49b115da-4c7d-4625-a16d-597277aeb6e4\n", "  Args:\n", "    done: True\n"]}], "source": ["for m in output['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "dc8367c4", "metadata": {}, "source": ["This creates a graph that:\n", "1. Starts with an LLM decision\n", "2. Conditionally routes to tool execution or termination\n", "3. After tool execution, returns to LLM for the next decision\n", "4. Repeats until completion or no tool is called\n"]}, {"cell_type": "markdown", "id": "b2b3406d-496d-43c9-942e-c5ce7e3a8321", "metadata": {}, "source": ["### Combine workflow with our agent\n", "\n", "We can combine the router and the agent."]}, {"cell_type": "code", "execution_count": 51, "id": "697f2548-b5a5-4fb6-8aed-226369e53e25", "metadata": {}, "outputs": [], "source": ["overall_workflow = (\n", "    StateGraph(State)\n", "    .add_node(triage_router)\n", "    .add_node(\"response_agent\", agent)\n", "    .add_edge(START, \"triage_router\")\n", ").compile()"]}, {"cell_type": "code", "execution_count": 52, "id": "2dd6dcc4-6346-4d41-ae36-61f3fc83b7a7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["show_graph(overall_workflow, xray=True)"]}, {"cell_type": "markdown", "id": "2091d5cc", "metadata": {}, "source": ["This is a higher-level composition where:\n", "1. First, the triage router analyzes the email\n", "2. If needed, the response agent handles crafting a response\n", "3. The workflow ends when either the triage decides no response is needed or the response agent completes"]}, {"cell_type": "code", "execution_count": 53, "id": "070f18a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔔 Classification: NOTIFY - This email contains important information\n"]}], "source": ["email_input = {\n", "    \"author\": \"System Admin <<EMAIL>>\",\n", "    \"to\": \"Development Team <<EMAIL>>\",\n", "    \"subject\": \"Scheduled maintenance - database downtime\",\n", "    \"email_thread\": \"Hi team,\\n\\nThis is a reminder that we'll be performing scheduled maintenance on the production database tonight from 2AM to 4AM EST. During this time, all database services will be unavailable.\\n\\nPlease plan your work accordingly and ensure no critical deployments are scheduled during this window.\\n\\nThanks,\\nSystem Admin Team\"\n", "}\n", "\n", "# Run the agent\n", "response = overall_workflow.invoke({\"email_input\": email_input})\n", "for m in response[\"messages\"]:\n", "    m.pretty_print()"]}, {"cell_type": "code", "execution_count": 60, "id": "82ecc299", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Respond to the email: \n", "\n", "\n", "\n", "**Subject**: Quick question about API documentation\n", "**From**: <PERSON> <<EMAIL>>\n", "**To**: <PERSON> <<EMAIL>>\n", "\n", "<PERSON>,\n", "I was reviewing the API documentation for the new authentication service and noticed a few endpoints seem to be missing from the specs. Could you help clarify if this was intentional or if we should update the docs?\n", "Specifically, I'm looking at:\n", "- /auth/refresh\n", "- /auth/validate\n", "Thanks!\n", "<PERSON>\n", "\n", "---\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  write_email (7ca92676-5eaa-4bb7-9e6f-70ed33c1fbb0)\n", " Call ID: 7ca92676-5eaa-4bb7-9e6f-70ed33c1fbb0\n", "  Args:\n", "    to: <EMAIL>\n", "    content: <PERSON>,\n", "\n", "Thanks for pointing this out. I'll investigate whether the omission of the /auth/refresh and /auth/validate endpoints was intentional and get back to you with a confirmation or an updated version of the documentation by the end of the day.\n", "\n", "Best,\n", "<PERSON>\n", "    subject: Re: Quick question about API documentation\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "Email <NAME_EMAIL> with subject 'Re: Quick question about API documentation' and content: <PERSON>,\n", "\n", "Thanks for pointing this out. I'll investigate whether the omission of the /auth/refresh and /auth/validate endpoints was intentional and get back to you with a confirmation or an updated version of the documentation by the end of the day.\n", "\n", "Best,\n", "<PERSON>\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Done (35e6d1c8-968e-4a1c-9880-e14d4c5c3303)\n", " Call ID: 35e6d1c8-968e-4a1c-9880-e14d4c5c3303\n", "  Args:\n", "    done: True\n"]}], "source": ["response\n", "for m in response[\"messages\"]:\n", "    m.pretty_print()"]}, {"cell_type": "code", "execution_count": 54, "id": "7a50ae0a-7bd1-4e69-90be-781b1e77b4dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📧 Classification: RESPOND - This email requires a response\n", "LLM_call is calling\n", "Should continue is calling\n", "<PERSON><PERSON><PERSON><PERSON><PERSON> is calling\n", "LLM_call is calling\n", "Should continue is calling\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Respond to the email: \n", "\n", "\n", "\n", "**Subject**: Quick question about API documentation\n", "**From**: <PERSON> <<EMAIL>>\n", "**To**: <PERSON> <<EMAIL>>\n", "\n", "<PERSON>,\n", "I was reviewing the API documentation for the new authentication service and noticed a few endpoints seem to be missing from the specs. Could you help clarify if this was intentional or if we should update the docs?\n", "Specifically, I'm looking at:\n", "- /auth/refresh\n", "- /auth/validate\n", "Thanks!\n", "<PERSON>\n", "\n", "---\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  write_email (7ca92676-5eaa-4bb7-9e6f-70ed33c1fbb0)\n", " Call ID: 7ca92676-5eaa-4bb7-9e6f-70ed33c1fbb0\n", "  Args:\n", "    to: <EMAIL>\n", "    content: <PERSON>,\n", "\n", "Thanks for pointing this out. I'll investigate whether the omission of the /auth/refresh and /auth/validate endpoints was intentional and get back to you with a confirmation or an updated version of the documentation by the end of the day.\n", "\n", "Best,\n", "<PERSON>\n", "    subject: Re: Quick question about API documentation\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "Email <NAME_EMAIL> with subject 'Re: Quick question about API documentation' and content: <PERSON>,\n", "\n", "Thanks for pointing this out. I'll investigate whether the omission of the /auth/refresh and /auth/validate endpoints was intentional and get back to you with a confirmation or an updated version of the documentation by the end of the day.\n", "\n", "Best,\n", "<PERSON>\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Done (35e6d1c8-968e-4a1c-9880-e14d4c5c3303)\n", " Call ID: 35e6d1c8-968e-4a1c-9880-e14d4c5c3303\n", "  Args:\n", "    done: True\n"]}], "source": ["email_input = {\n", "  \"author\": \"<PERSON> <<EMAIL>>\",\n", "  \"to\": \"<PERSON> <<EMAIL>>\",\n", "  \"subject\": \"Quick question about API documentation\",\n", "  \"email_thread\": \"<PERSON> <PERSON>,\\nI was reviewing the API documentation for the new authentication service and noticed a few endpoints seem to be missing from the specs. Could you help clarify if this was intentional or if we should update the docs?\\nSpecifically, I'm looking at:\\n- /auth/refresh\\n- /auth/validate\\nThanks!\\nAlice\"\n", "}\n", "\n", "# Run the agent\n", "response = overall_workflow.invoke({\"email_input\": email_input})\n", "for m in response[\"messages\"]:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "f631f61f", "metadata": {}, "source": ["## Testing with Local Deployment\n", "\n", "You can find the file for our agent in the `src/email_assistant` directory:\n", "\n", "* `src/email_assistant/email_assistant.py`\n", "\n", "You can test them locally in LangGraph Studio by running:\n", "\n", "```\n", "! langgraph dev\n", "```"]}, {"cell_type": "markdown", "id": "12752016", "metadata": {"lines_to_next_cell": 0}, "source": ["Example e-mail you can test:"]}, {"cell_type": "code", "execution_count": null, "id": "08ee005a", "metadata": {}, "outputs": [], "source": ["{\n", "  \"author\": \"<PERSON> <<EMAIL>>\",\n", "  \"to\": \"<PERSON> <<EMAIL>>\",\n", "  \"subject\": \"Quick question about API documentation\",\n", "  \"email_thread\": \"<PERSON> <PERSON>,\\nI was reviewing the API documentation for the new authentication service and noticed a few endpoints seem to be missing from the specs. Could you help clarify if this was intentional or if we should update the docs?\\nSpecifically, I'm looking at:\\n- /auth/refresh\\n- /auth/validate\\nThanks!\\nAlice\"\n", "}"]}, {"cell_type": "markdown", "id": "d09e33b6", "metadata": {}, "source": ["![studio-img](img/studio.png)"]}, {"cell_type": "code", "execution_count": null, "id": "0d195e21-f2c5-4762-a4f0-c8d7459df6d5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "main_language": "python", "notebook_metadata_filter": "-all"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}