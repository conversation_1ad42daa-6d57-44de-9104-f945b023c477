import re
import os
import random
from time import sleep
import argparse
from datetime import datetime
import config

from telethon import TelegramClient, events
from telethon.tl.types import InputPeerUser, InputPeerChannel, MessageEntityUrl, InputChannel
from telethon.tl.functions.messages import GetBotCallbackAnswerRequest
from telethon.errors import Flood<PERSON>ait<PERSON>rror, SessionPasswordNeededError

from langchain_google_genai import ChatG<PERSON>gleGenerativeA<PERSON>
from langchain_core.messages import HumanMessage
from langchain import hub
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.graph import START, StateGraph
from typing_extensions import List, TypedDict
from langchain_core.messages import SystemMessage
from langgraph.prebuilt import ToolNode


async def get_client():
    client = TelegramClient(
        session=config.SESSION_NAME,
        api_hash=config.API_HASH,
        api_id=config.API_ID,
    )
    await client.start()
    
    if not await client.is_user_authorized():
        await client.send_code_request(config.PHONE_NUMBER)
        code = input('Enter the code: ')
        await client.sign_in(config.PHONE_NUMBER, code)
        
    return client


class TeleBot:
    def __init__(self, tg_client):
        self.tg_client = tg_client
        self._handler = None

    async def rawMessage(self, channel_link: str, message_limit: int):
        try:
            channel_entity = await self.tg_client.get_entity(channel_link)
            messages = self.tg_client.iter_messages(channel_entity, limit=message_limit)
            return messages
        
        except FloodWaitError as e:
            print(f"FloodWaitError: Wait {e.seconds} seconds.")
            await asyncio.sleep(e.seconds)
            
        except Exception as e:
            print(f"Error getting channel messages: {e}")
            return None

    async def setupMessageHandler(self, channel_link: str):
        """Setup listener for new messages in the specified channel"""
        if self._handler:
            self.tg_client.remove_event_handler(self._handler)

        try:
            # Get the channel entity first
            channel_entity = await self.tg_client.get_entity(channel_link)
            print(f"✅ Connected to channel: {channel_entity.title}")
            
            @self.tg_client.on(events.NewMessage(chats=channel_entity))
            async def handler(event):
                message = event.message
                print(f"🔔 New message from {channel_entity.title}:")
                print(f"📝 {message.text}")
                print(f"⏰ {message.date}")
                print("-" * 50)
                return(message)
                
            self._handler = handler
            print("🎧 Message listener started. Waiting for new messages...")
            
        except Exception as e:
            print(f"❌ Error setting up message handler: {e}")


async def main():
    client = None
    try:
        client = await get_client()
        bot = TeleBot(client)

        channel_link = "ocutesting"
        print(f"Setting up listener for channel: {channel_link}")
        
        # Setup the message handler
        message = await bot.setupMessageHandler(channel_link)
  
        # Keep the client running to listen for messages
        print("Listening for messages... Press Ctrl+C to stop")
        await client.run_until_disconnected()
        
    except KeyboardInterrupt:
        print("\n👋 Stopping listener...")
    except Exception as e:
        print(f"❌ Main error: {e}")
    finally:
        if client:
            await client.disconnect()


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())