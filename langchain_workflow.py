"""
LangChain Workflow Implementation
Converted from <PERSON>py<PERSON> notebook to Python script
"""

import os
from typing import Literal
from dotenv import load_dotenv
from pydantic import BaseModel, Field

from langchain.chat_models import init_chat_model
from langchain.tools import tool
from langgraph.graph import MessagesState, StateGraph, START, END
from langgraph.types import Command


from main import TeleBot, get_client
from telethon import events

from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class TelegramMessageInput(BaseModel):
    text: str
    date: datetime
    media_url: Optional[str] = None
    is_forwarded: Optional[bool] = False
    is_post: Optional[bool] = False
    from_channel: Optional[str] = None
    link_preview_title: Optional[str] = None
    link_preview_description: Optional[str] = None
    link_preview_site: Optional[str] = None
    all_links: Optional[List[str]] = None
    bold_entities: Optional[List[str]] = None

def extract_telegram_input(message):
    entities = message.entities or []
    bolds = []
    for ent in entities:
        if ent.__class__.__name__ == "MessageEntityBold":
            bolds.append(message.message[ent.offset:ent.offset+ent.length])

    all_links = []
    if message.entities:
        for ent in message.entities:
            if ent.__class__.__name__ == "MessageEntityUrl":
                all_links.append(message.message[ent.offset:ent.offset+ent.length])

    media_url = None
    title = desc = site = None
    if getattr(message.media, "webpage", None):
        media_url = message.media.webpage.url
        title = message.media.webpage.title
        desc = message.media.webpage.description
        site = message.media.webpage.site_name

    return TelegramMessageInput(
        text=message.message,
        date=message.date,
        media_url=media_url,
        is_forwarded=bool(message.fwd_from),
        is_post=message.post,
        from_channel=str(message.peer_id.channel_id),
        link_preview_title=title,
        link_preview_description=desc,
        link_preview_site=site,
        all_links=all_links,
        bold_entities=bolds
    )


# Load environment variables
load_dotenv(".env", override=True)

# Initialize LLM
llm = init_chat_model("gemini-2.5-pro", model_provider="google_genai")


class State(MessagesState):
    """State class for the workflow"""
    # We can add a specific key to our state for the email input
    post_input: dict
    classification_decision: Literal["ignore", "notify", "take_action"]


class ProjectSchema(BaseModel):
    """Analyze the project"""
    
    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    project_name: str = Field(
        description="The project name."
    )
    funding: int = Field(
        description="Function of the projet "
    )
    Rating: int = Field(
        description="Rating of the project"
    )
    project_type: str = Field(
        description="Type of the project"
    )
    project_category: str = Field(
        description="Category of the project"
    )


class RouterSchema(BaseModel):
    """Analyze the post and route it according to its content."""

    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    classification: Literal["ignore", "notify", "take_action"] = Field(
        description="The classification of an post: 'ignore' for irrelevant post, "
        "'notify' for important information that doesn't require response, "
        "'take_action' for post that requires a action like to create a task, create a page",
    )


# Create router LLM with structured output
llm_router = llm.with_structured_output(RouterSchema)


def triage_router(state: State) -> Command[Literal["action_agent", "__end__"]]:
    """Analyze post content to decide if we should create a task, notify, or ignore."""



    # Import prompts (assuming they exist in TelegramAi.prompts module)
    try:
        from TelegramAi.prompts import (
            triage_system_prompt, 
            default_background, 
            default_triage_instructions, 
            triage_user_prompt, 
            AGENT_TOOLS_PROMPT, 
            default_response_preferences, 
            agent_system_prompt
        )
    except ImportError:
        # Fallback prompts if module doesn't exist
        triage_system_prompt = "You are a helpful assistant that analyzes posts. Background: {background}. Instructions: {triage_instructions}"
        default_background = "You are analyzing social media posts for relevance."
        default_triage_instructions = "Classify posts as ignore, notify, or take_action based on their content."
        triage_user_prompt = "Analyze this post: {post_thread}"
        AGENT_TOOLS_PROMPT = "You have access to tools for creating tasks and pages."
        default_response_preferences = "Be concise and helpful."
        agent_system_prompt = "You are an agent with tools. {tools_prompt}. Background: {background}. Preferences: {response_preferences}"
    
    # Parse the post input
    post_thread = state["post_input"]

    system_prompt = triage_system_prompt.format(
        background=default_background,
        triage_instructions=default_triage_instructions
    )
    
    user_prompt = triage_user_prompt.format(
        post_thread=post_thread
    )
    
    # Run the router LLM
    result = llm_router.invoke(
        [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
    )

    # Decision and routing using Command pattern (like email_assistant.py)
    if result.classification == "take_action":
        print("📧 Classification: Take Action - This post requires to create a task")
        goto = "action_agent"
        update = {
            "messages": [
                {
                    "role": "user",
                    "content": f"Take action for this post: \n\n{post_thread}",
                }
            ],
            "classification_decision": result.classification,
        }

    elif result.classification == "ignore":
        print("🚫 Classification: IGNORE - This post can be safely ignored")
        goto = END
        update = {
            "classification_decision": result.classification,
        }

    elif result.classification == "notify":
        print("🔔 Classification: NOTIFY - This post contains important information")
        goto = END
        update = {
            "classification_decision": result.classification,
        }
    else:
        raise ValueError(f"Invalid classification: {result.classification}")

    return Command(goto=goto, update=update)


# Define tools
@tool
def create_task(project: str, task_name: str, priority: str, due_date: str) -> str:
    """Create task for a specific project."""
    # Placeholder response - in real app would send email
    return f"Created task for {project} project. and task is {task_name}, priority is {priority} and due date is {due_date}"


@tool
def create_page(project: str) -> str:
    """Create page for a specific project."""
    # Placeholder response - in real app would send email
    return f"Created page for {project} project."


@tool
class Done(BaseModel):
    """E-mail has been sent."""
    done: bool


# Setup tools
tools = [create_task, create_page, Done]
tools_by_name = {tool.name: tool for tool in tools}

model_with_tools = llm.bind_tools(tools, tool_choice="any", parallel_tool_calls=False)


def llm_call(state: State):
    """LLM decides whether to call a tool or not"""
    try:
        from TelegramAi.prompts import (
            agent_system_prompt,
            AGENT_TOOLS_PROMPT,
            default_background,
            default_response_preferences
        )
    except ImportError:
        # Fallback prompts
        agent_system_prompt = "You are an agent with tools. {tools_prompt}. Background: {background}. Preferences: {response_preferences}"
        AGENT_TOOLS_PROMPT = "You have access to tools for creating tasks and pages."
        default_background = "You are analyzing social media posts for relevance."
        default_response_preferences = "Be concise and helpful."
    
    output = {
        "messages": [
            model_with_tools.invoke(
                [
                    {"role": "system", "content": agent_system_prompt.format(
                        tools_prompt=AGENT_TOOLS_PROMPT,
                        background=default_background,
                        response_preferences=default_response_preferences,
                        )
                    },
                    
                ]
                + state["messages"]
            )
        ]
    }
    return output


def tool_handler(state: State):
    """Performs the tool call."""

    # List for tool messages
    result = []
    
    # Iterate through tool calls
    for tool_call in state["messages"][-1].tool_calls:
        # Get the tool
        tool = tools_by_name[tool_call["name"]]
        # Run it
        observation = tool.invoke(tool_call["args"])
        # Create a tool message
        result.append({"role": "tool", "content": observation, "tool_call_id": tool_call["id"]})
    
    # Add it to our messages
    return {"messages": result}


def should_continue(state: State) -> Literal["tool_handler", "__end__"]:
    """Route to tool handler, or end if Done tool called."""

    # Get the last message
    messages = state["messages"]
    last_message = messages[-1]

    # Check if there are tool calls
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        for tool_call in last_message.tool_calls:
            if tool_call["name"] == "Done":
                print("✅ Done tool called - ending workflow")
                return END
            else:
                print(f"🔧 Tool call found: {tool_call['name']} - routing to tool_handler")
                return "tool_handler"
    else:
        # No tool calls, end the workflow
        print("🏁 No tool calls found - ending workflow")
        return END


def create_workflow():
    """Create and compile the workflow"""
    
    # Build workflow
    workflow = StateGraph(State)

    # Add nodes
    workflow.add_node("llm_call", llm_call)
    workflow.add_node("tool_handler", tool_handler)

    # Add edges
    workflow.add_edge(START, "llm_call")
    workflow.add_conditional_edges(
        "llm_call",
        should_continue,
        {
            "tool_handler": "tool_handler",
            END: END,
        },
    )
    workflow.add_edge("tool_handler", "llm_call")

    # Compile the agent
    agent = workflow.compile()
    
    # Create overall workflow (using Command pattern like email_assistant.py)
    overall_workflow = (
        StateGraph(State)
        .add_node(triage_router)
        .add_node("action_agent", agent)
        .add_edge(START, "triage_router")
    ).compile()
    
    return overall_workflow


async def process_message(message_text: str, workflow, config):
    """Process a single message through the workflow"""
    try:
        response = await workflow.ainvoke({"post_input": message_text}, config)

        # Print results
        print("\n=== Workflow Results ===")
        if "messages" in response:
            for m in response["messages"]:
                m.pretty_print()

        return response

    except Exception as e:
        print(f"Error running workflow: {e}")
        return None


async def main():
    """Main function to run the workflow with Telegram integration"""
    client = None
    try:
        # Create workflow
        workflow = create_workflow()
        config = {"configurable": {"thread_id": "1"}}

        # Initialize Telegram client
        client = await get_client()
        bot = TeleBot(client)

        channel_link = "ocutesting"

        # Get the channel entity
        channel_entity = await client.get_entity(channel_link)
        print(f"✅ Connected to channel: {channel_entity.title}")

        # Setup message handler with workflow integration
        @client.on(events.NewMessage(chats=channel_entity))
        async def message_handler(event):

            message = event.message
            message_dict = message.to_dict()

            print(message_dict.keys())
            
            if message.text:  # Only process text messages
                print(f"🔔 New message from {channel_entity.title}:")
                print(f"📝 {message.text}")
                print(f"⏰ {message.date}")
                print("-" * 50)

                # Process the message through the workflow
                # await process_message(message, workflow, config)
                print("=" * 50)

        print("🎧 Message listener started. Waiting for new messages...")
        print("Press Ctrl+C to stop")

        # Keep the client running
        await client.run_until_disconnected()

    except KeyboardInterrupt:
        print("\n👋 Stopping listener...")
    except Exception as e:
        print(f"❌ Main error: {e}")
    finally:
        if client:
            await client.disconnect()


async def run_sample_test():
    """Run a sample test without Telegram integration"""
    # Sample post data for testing
    sample_post = """
     Opensea Airdrop — New tasks are Live ✔️

    🔗 Link — https://opensea.io/rewards

    1️⃣. Buy NFT from a Verified Collection on any chain [ Minimum $5 USD ]
    ✨  50 XP

    🖼 NFT Collection [ Base Chain - Cost $17 ]  — https://opensea.io/collection/dxterminal

    🟢 Buy this NFT
    🟢 Claim XP
    🟢 Sell NFT
    ✅ Done
    """

    workflow = create_workflow()
    config = {"configurable": {"thread_id": "1"}}

    print("Testing workflow with sample data...")
    await process_message(sample_post, workflow, config)


if __name__ == "__main__":
    import asyncio

    # Uncomment the line below to run sample test instead of Telegram integration
    # asyncio.run(run_sample_test())

    # Run with Telegram integration
    asyncio.run(main())