from main import get_client, TeleBot
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime
import re

def extract_telegram_message(message) -> Dict[str, Any]:
    # Extract the main text
    text = message.message or ""
    
    # Build metadata dictionary with all the extra stuff
    metadata = {
        "date": message.date,
        "is_forwarded": bool(message.fwd_from),
        "is_post": message.post,
        "from_channel": str(message.peer_id.channel_id) if message.peer_id else None,
        "message_id": message.id,
        "all_links": re.findall(r'https?://\S+', text),
    }
    
    # Add media/webpage info if it exists
    if message.media and getattr(message.media, 'webpage', None):
        webpage = message.media.webpage
        metadata.update({
            "media_url": getattr(webpage, 'url', None),
            "link_preview_title": getattr(webpage, 'title', None),
            "link_preview_description": getattr(webpage, 'description', None),
            "link_preview_site": getattr(webpage, 'site_name', None),
        })
    
    # Add forward info if message is forwarded
    if message.fwd_from:
        metadata["forward_info"] = {
            "from_id": str(message.fwd_from.from_id) if message.fwd_from.from_id else None,
            "from_name": message.fwd_from.from_name,
            "date": message.fwd_from.date,
        }
    
    # Extract bold entities
    """
    bold_entities = []
    if hasattr(message, 'entities') and message.entities:
        for entity in message.entities:
            if entity.__class__.__name__ == "MessageEntityBold":
                offset = entity.offset
                length = entity.length
                bold_entities.append(text[offset:offset+length])
    
    if bold_entities:
        metadata["bold_entities"] = bold_entities
    """
    
    # Return as plain dict
    return {
        "text": text,
        "metadata": metadata
    }
