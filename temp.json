{"object": "list", "results": [{"object": "page", "id": "150b9836-6aec-800d-aff3-f2c88fa13b73", "created_time": "2024-12-02T09:06:00.000Z", "last_edited_time": "2024-12-10T10:38:00.000Z", "created_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "last_edited_by": {"object": "user", "id": "62887749-840c-4352-91f4-5f6fff8eff2c"}, "cover": {"type": "external", "external": {"url": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR3GxqFCTAypjXenCaTifUeKMW2TjnzIYilZg&s"}}, "icon": {"type": "external", "external": {"url": "https://www.notion.so/icons/apple_gray.svg"}}, "parent": {"type": "database_id", "database_id": "150b9836-6aec-81ed-b6a0-c7e8e84468bd"}, "archived": false, "in_trash": false, "properties": {"Category": {"id": "Ab~n", "type": "multi_select", "multi_select": [{"id": "iFrS", "name": "Bridge", "color": "purple"}, {"id": "ZcSn", "name": "Layer 2", "color": "pink"}]}, "Chain/Tech": {"id": "CY%5E%3E", "type": "select", "select": {"id": "`Oit", "name": "Etherium", "color": "purple"}}, "Multiple Account": {"id": "E%3EaB", "type": "select", "select": null}, "Reward Landed": {"id": "Ey%7Dz", "type": "number", "number": null}, "Usefull LInks": {"id": "IhWc", "type": "rich_text", "rich_text": []}, "Social": {"id": "JII_", "type": "url", "url": null}, "Airdrop Status": {"id": "RbW_", "type": "select", "select": {"id": "\\[ZA", "name": "<PERSON><PERSON> Confirmed", "color": "default"}}, "Cost": {"id": "ZGZm", "type": "multi_select", "multi_select": [{"id": "xryQ", "name": "Free", "color": "green"}]}, "Progress": {"id": "%60aWd", "type": "status", "status": {"id": "904b3dc7-90b3-4360-9305-d5c8e450d831", "name": "Done", "color": "green"}}, "Tasks": {"id": "a%5Cad", "type": "relation", "relation": [], "has_more": false}, "Raised ($M)": {"id": "a%5EJU", "type": "rich_text", "rich_text": []}, "Potential": {"id": "bQRn", "type": "select", "select": {"id": "tN<e", "name": "Tier S", "color": "green"}}, "Stage": {"id": "bpjB", "type": "select", "select": {"id": "jICo", "name": "Alpha", "color": "yellow"}}, "Website": {"id": "fXM%60", "type": "url", "url": null}, "Date": {"id": "gpRK", "type": "date", "date": null}, "Name": {"id": "title", "type": "title", "title": [{"type": "text", "text": {"content": "Movement Lab", "link": null}, "annotations": {"bold": false, "italic": false, "strikethrough": false, "underline": false, "code": false, "color": "default"}, "plain_text": "Movement Lab", "href": null}]}}, "url": "https://www.notion.so/Movement-Lab-150b98366aec800daff3f2c88fa13b73", "public_url": null}], "next_cursor": null, "has_more": false, "type": "page_or_database", "page_or_database": {}, "request_id": "c4eaaa00-2e7a-4d03-9a21-73f470ccecfc"}