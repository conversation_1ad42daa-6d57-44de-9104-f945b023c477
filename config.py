import os
from dotenv import load_dotenv

load_dotenv()

API_ID = os.getenv("API_ID")
API_HASH = os.getenv("API_HASH")
PHONE_NUMBER = os.getenv("PHONE_NUMBER")
SESSION_NAME = os.getenv("SESSION_NAME", "telegram_monitor")
BOT_TOKEN = os.getenv("BOT_TOKEN")

# You can add multiple channel IDs here, separated by commas in your .env file
TARGET_CHANNEL_IDS_STR = os.getenv("TARGET_CHANNEL_IDS")
if not TARGET_CHANNEL_IDS_STR:
    raise ValueError("TARGET_CHANNEL_IDS not found in .env file")
TARGET_CHANNEL_IDS = TARGET_CHANNEL_IDS_STR.strip()

DATABASE_NAME = "messages.db"