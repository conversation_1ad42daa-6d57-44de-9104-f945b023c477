{"cells": [{"cell_type": "markdown", "id": "07c57479", "metadata": {}, "source": ["# Agents with Human-in-the-Loop\n", "\n", "We have an email assistant that uses a router to triage emails and then passes the email to the agent for response generation. We've also evaluated it. But do we fully *trust* it to manage our inbox autonomously? For such a sensitive task, human-in-the-loop (HITL) is important! Here we'll show how to add a human-in-the-loop to our email assistant so that we can review specific tool calls. \n", "\n", "![overview-img](img/overview_hitl.png)\n", "\n"]}, {"cell_type": "markdown", "id": "c8f73f12", "metadata": {}, "source": ["We're going to show how to make the graph *pause* at specific points and await human input.\n", "\n", "![overview-img](img/hitl_schematic.png)"]}, {"cell_type": "markdown", "id": "52e3532e", "metadata": {}, "source": ["#### Load Environment Variables"]}, {"cell_type": "code", "execution_count": 1, "id": "a57594a7", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "load_dotenv(\"../.env\")"]}, {"cell_type": "markdown", "id": "2566464d", "metadata": {}, "source": ["## Adding HITL to our email assistant\n", "\n", "Let's add HITL to our email assistant. \n", "\n", "We can start with tools, just as we did before. \n", "\n", "But now, we'll add a new tool Question that allows the assistant to ask the user a question."]}, {"cell_type": "code", "execution_count": 3, "id": "6d4dfb07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "from typing import Literal\n", "from datetime import datetime\n", "from pydantic import BaseModel\n", "\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.tools import tool\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.types import interrupt, Command\n", "\n", "from email_assistant.prompts import triage_system_prompt, triage_user_prompt, agent_system_prompt_hitl, default_background, default_triage_instructions, default_response_preferences, default_cal_preferences\n", "from email_assistant.tools.default.prompt_templates import HITL_TOOLS_PROMPT\n", "from email_assistant.schemas import State, RouterSchema, StateInput\n", "from email_assistant.utils import parse_email, format_for_display, format_email_markdown\n", "\n", "# Agent tools \n", "@tool\n", "def write_email(to: str, subject: str, content: str) -> str:\n", "    \"\"\"Write and send an email.\"\"\"\n", "    # Placeholder response - in real app would send email\n", "    return f\"Email sent to {to} with subject '{subject}' and content: {content}\"\n", "\n", "@tool\n", "def schedule_meeting(\n", "    attendees: list[str], subject: str, duration_minutes: int, preferred_day: datetime, start_time: int\n", ") -> str:\n", "    \"\"\"Schedule a calendar meeting.\"\"\"\n", "    # Placeholder response - in real app would check calendar and schedule\n", "    date_str = preferred_day.strftime(\"%A, %B %d, %Y\")\n", "    return f\"Meeting '{subject}' scheduled on {date_str} at {start_time} for {duration_minutes} minutes with {len(attendees)} attendees\"\n", "\n", "@tool\n", "def check_calendar_availability(day: str) -> str:\n", "    \"\"\"Check calendar availability for a given day.\"\"\"\n", "    # Placeholder response - in real app would check actual calendar\n", "    return f\"Available times on {day}: 9:00 AM, 2:00 PM, 4:00 PM\"\n", "\n", "@tool\n", "# This is new! \n", "class Question(BaseModel):\n", "      \"\"\"Question to ask user.\"\"\"\n", "      content: str\n", "    \n", "@tool\n", "class Done(BaseModel):\n", "      \"\"\"E-mail has been sent.\"\"\"\n", "      done: bool\n", "\n", "# All tools available to the agent\n", "tools = [\n", "    write_email, \n", "    schedule_meeting, \n", "    check_calendar_availability, \n", "    Question, \n", "    Done,\n", "]\n", "\n", "tools_by_name = {tool.name: tool for tool in tools}\n", "\n", "# Initialize the LLM for use with router / structured output\n", "llm = init_chat_model(\"gemini-2.5-pro\", model_provider=\"google_genai\")\n", "llm_router = llm.with_structured_output(RouterSchema) \n", "\n", "# Initialize the LLM, enforcing tool use (of any available tools) for agent\n", "llm = init_chat_model(\"gemini-2.5-pro\", model_provider=\"google_genai\")\n", "llm_with_tools = llm.bind_tools(tools, tool_choice=\"required\")"]}, {"cell_type": "code", "execution_count": 4, "id": "bf05b260-9809-4f32-807b-abe1632e4181", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 1 </span>write_email(to, subject, content) - Send emails to specified recipients                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 2 </span>schedule_meeting(attendees, subject, duration_minutes, preferred_day, start_time) - Schedule calendar meetings  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>where preferred_day is a datetime object                                                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 3 </span>check_calendar_availability(day) - Check available time slots for a given day                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 4 </span>Question(content) - Ask the user any follow-up questions                                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 5 </span>Done - E-mail has been sent                                                                                     \n", "</pre>\n"], "text/plain": ["\n", "\u001b[1;33m 1 \u001b[0mwrite_email(to, subject, content) - Send emails to specified recipients                                         \n", "\u001b[1;33m 2 \u001b[0mschedule_meeting(attendees, subject, duration_minutes, preferred_day, start_time) - Schedule calendar meetings  \n", "\u001b[1;33m   \u001b[0mwhere preferred_day is a datetime object                                                                        \n", "\u001b[1;33m 3 \u001b[0mcheck_calendar_availability(day) - Check available time slots for a given day                                   \n", "\u001b[1;33m 4 \u001b[0mQuestion(content) - Ask the user any follow-up questions                                                        \n", "\u001b[1;33m 5 \u001b[0mDone - E-mail has been sent                                                                                     \n"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from rich.markdown import Markdown\n", "Markdown(HITL_TOOLS_PROMPT)"]}, {"cell_type": "markdown", "id": "b9f8f334", "metadata": {}, "source": ["#### Triage node\n", "\n", "We define a python function with our triage routing logic, just as we did before.\n", "\n", "But, if the classification is `notify`, we want to interrupt the graph to allow the user to review the email! \n", "\n", "So we go to a new node, `triage_interrupt_handler`."]}, {"cell_type": "code", "execution_count": 5, "id": "65efb689", "metadata": {}, "outputs": [], "source": ["def triage_router(state: State) -> Command[Literal[\"triage_interrupt_handler\", \"response_agent\", \"__end__\"]]:\n", "    \"\"\"Analyze email content to decide if we should respond, notify, or ignore.\"\"\"\n", "\n", "    # Parse the email input\n", "    author, to, subject, email_thread = parse_email(state[\"email_input\"])\n", "    user_prompt = triage_user_prompt.format(\n", "        author=author, to=to, subject=subject, email_thread=email_thread\n", "    )\n", "\n", "    # Create email markdown for Agent Inbox in case of notification  \n", "    email_markdown = format_email_markdown(subject, author, to, email_thread)\n", "\n", "    # Format system prompt with background and triage instructions\n", "    system_prompt = triage_system_prompt.format(\n", "        background=default_background,\n", "        triage_instructions=default_triage_instructions\n", "    )\n", "\n", "    # Run the router LLM\n", "    result = llm_router.invoke(\n", "        [\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt},\n", "        ]\n", "    )\n", "\n", "    # Decision\n", "    classification = result.classification\n", "\n", "    # Process the classification decision\n", "    if classification == \"respond\":\n", "        print(\"📧 Classification: RESPOND - This email requires a response\")\n", "        # Next node\n", "        goto = \"response_agent\"\n", "        # Update the state\n", "        update = {\n", "            \"classification_decision\": classification,\n", "            \"messages\": [{\"role\": \"user\",\n", "                            \"content\": f\"Respond to the email: {email_markdown}\"\n", "                        }],\n", "        }\n", "    elif classification == \"ignore\":\n", "        print(\"🚫 Classification: IGNORE - This email can be safely ignored\")\n", "        # Next node\n", "        goto = END\n", "        # Update the state\n", "        update = {\n", "            \"classification_decision\": classification,\n", "        }\n", "\n", "    elif classification == \"notify\":\n", "        print(\"🔔 Classification: NOTIFY - This email contains important information\") \n", "        # This is new! \n", "        goto = \"triage_interrupt_handler\"\n", "        # Update the state\n", "        update = {\n", "            \"classification_decision\": classification,\n", "        }\n", "\n", "    else:\n", "        raise ValueError(f\"Invalid classification: {classification}\")\n", "    return Command(goto=goto, update=update)"]}, {"cell_type": "markdown", "id": "4a1f564a", "metadata": {}, "source": ["#### Triage Interrupt Handler\n", "\n", "If the decision is to `notify` the user, we interrupt the graph! \n", "\n", "![overview-img](img/HITL_flow_triage.png)\n", "\n", "For this, we add a new node, `triage_interrupt_handler`, that will: \n", "\n", "1. Show the classification to the user if it is `notify`: We'll pass a `dict` to the interrupt that contains our classification. \n", "2. Allow the user to respond to the decision: We'll design the code to handle what we will get back from Agent Inbox. \n", "\n", "As you can see [here](https://github.com/langchain-ai/agent-inbox?tab=readme-ov-file#what-do-the-fields-mean), we format our interrupt with specific fields so that it can be viewed in Agent Inbox:\n", "\n", "* `action_request`: The action and arguments for the interrupt with `action` (the action name) and `args` (the tool call arguments). This is rendered in the Agent Inbox as the main header for the interrupt event.\n", "* `config`: Configures which interaction types are allowed, and specific UI elements for each. \n", "* `description`: Should be detailed, and may be markdown. This will be rendered in the Agent Inbox as the description\n"]}, {"cell_type": "code", "execution_count": 6, "id": "203346bb", "metadata": {}, "outputs": [], "source": ["def triage_interrupt_handler(state: State) -> Command[Literal[\"response_agent\", \"__end__\"]]:\n", "    \"\"\"<PERSON><PERSON> interrupts from the triage step.\"\"\"\n", "    \n", "    # Parse the email input\n", "    author, to, subject, email_thread = parse_email(state[\"email_input\"])\n", "\n", "    # Create email markdown for Agent Inbox in case of notification  \n", "    email_markdown = format_email_markdown(subject, author, to, email_thread)\n", "\n", "    # Create messages\n", "    messages = [{\"role\": \"user\",\n", "                \"content\": f\"Email to notify user about: {email_markdown}\"\n", "                }]\n", "\n", "    # Create interrupt that is shown to the user\n", "    request = {\n", "        \"action_request\": {\n", "            \"action\": f\"Email Assistant: {state['classification_decision']}\",\n", "            \"args\": {}\n", "        },\n", "        \"config\": {\n", "            \"allow_ignore\": True,  \n", "            \"allow_respond\": True, \n", "            \"allow_edit\": <PERSON><PERSON><PERSON>, \n", "            \"allow_accept\": <PERSON><PERSON><PERSON>,  \n", "        },\n", "        # Email to show in Agent Inbox\n", "        \"description\": email_markdown,\n", "    }\n", "\n", "    # Agent Inbox responds with a list of dicts with a single key `type` that can be `accept`, `edit`, `ignore`, or `response`.  \n", "    response = interrupt([request])[0]\n", "\n", "    # If user provides feedback, go to response agent and use feedback to respond to email   \n", "    if response[\"type\"] == \"response\":\n", "        # Add feedback to messages \n", "        user_input = response[\"args\"]\n", "        # Used by the response agent\n", "        messages.append({\"role\": \"user\",\n", "                        \"content\": f\"User wants to reply to the email. Use this feedback to respond: {user_input}\"\n", "                        })\n", "        # Go to response agent\n", "        goto = \"response_agent\"\n", "\n", "    # If user ignores email, go to END\n", "    elif response[\"type\"] == \"ignore\":\n", "        goto = END\n", "\n", "    # Catch all other responses\n", "    else:\n", "        raise ValueError(f\"Invalid response: {response}\")\n", "\n", "    # Update the state \n", "    update = {\n", "        \"messages\": messages,\n", "    }\n", "\n", "    return Command(goto=goto, update=update)"]}, {"cell_type": "markdown", "id": "8613e4c4", "metadata": {}, "source": ["#### LLM call\n", "\n", "The `llm_call` node is the same as before:"]}, {"cell_type": "code", "execution_count": 7, "id": "036aba96", "metadata": {}, "outputs": [], "source": ["def llm_call(state: State):\n", "    \"\"\"LLM decides whether to call a tool or not.\"\"\"\n", "\n", "    return {\n", "        \"messages\": [\n", "            llm_with_tools.invoke(\n", "                [\n", "                    {\"role\": \"system\", \"content\": agent_system_prompt_hitl.format(tools_prompt=HITL_TOOLS_PROMPT, \n", "                                                                                  background=default_background,\n", "                                                                                  response_preferences=default_response_preferences, \n", "                                                                                  cal_preferences=default_cal_preferences)}\n", "                ]\n", "                + state[\"messages\"]\n", "            )\n", "        ]\n", "    }"]}, {"cell_type": "markdown", "id": "397516ee", "metadata": {}, "source": ["#### Interrupt Handler\n", "\n", "The `interrupt_handler` is the core HITL component of our response agent. \n", "\n", "Its job is to examine the tool calls that the LLM wants to make and determine which ones need human review before execution. Here's how it works:\n", "\n", "1. **Tool Selection**: The handler maintains a list of \"HITL tools\" that require human approval:\n", "   - `write_email`: Since sending emails has significant external impact\n", "   - `schedule_meeting`: Since scheduling meetings affects calendars\n", "   - `Question`: Since asking users questions requires direct interaction\n", "\n", "2. **Direct Execution**: Tools not in the HITL list (like `check_calendar_availability`) are executed immediately without interruption. This allows low-risk operations to proceed automatically.\n", "\n", "3. **Context Preparation**: For tools requiring review, the handler:\n", "   - Retrieves the original email for context\n", "   - Formats the tool call details for clear display\n", "   - Configures which interaction types are allowed for each tool type\n", "\n", "4. **Interrupt Creation**: The handler creates a structured interrupt request with:\n", "   - The action name and arguments\n", "   - Configuration for allowed interaction types\n", "   - A description that includes both the original email and the proposed action\n", "\n", "5. **Response Processing**: After the interrupt, the handler processes the human response:\n", "   - **Accept**: Executes the tool with original arguments\n", "   - **Edit**: Updates the tool call with edited arguments and then executes\n", "   - **Ignore**: Cancels the tool execution\n", "   - **Response**: Records feedback without execution\n", "\n", "This handler ensures humans have oversight of all significant actions while allowing routine operations to proceed automatically. \n", "\n", "The ability to edit tool arguments (like email content or meeting details) gives users precise control over the assistant's actions.\n", "\n", "We can visualize the overall flow: \n", "\n", "![overview-img](img/HITL_flow.png)"]}, {"cell_type": "code", "execution_count": 8, "id": "f41929d5", "metadata": {}, "outputs": [], "source": ["def interrupt_handler(state: State) -> Command[Literal[\"llm_call\", \"__end__\"]]:\n", "    \"\"\"Creates an interrupt for human review of tool calls\"\"\"\n", "    \n", "    # Store messages\n", "    result = []\n", "\n", "    # Go to the LLM call node next\n", "    goto = \"llm_call\"\n", "\n", "    # Iterate over the tool calls in the last message\n", "    for tool_call in state[\"messages\"][-1].tool_calls:\n", "        \n", "        # Allowed tools for HITL\n", "        hitl_tools = [\"write_email\", \"schedule_meeting\", \"Question\"]\n", "        \n", "        # If tool is not in our HITL list, execute it directly without interruption\n", "        if tool_call[\"name\"] not in hitl_tools:\n", "\n", "            # Execute tool without interruption\n", "            tool = tools_by_name[tool_call[\"name\"]]\n", "            observation = tool.invoke(tool_call[\"args\"])\n", "            result.append({\"role\": \"tool\", \"content\": observation, \"tool_call_id\": tool_call[\"id\"]})\n", "            continue\n", "            \n", "        # Get original email from email_input in state\n", "        email_input = state[\"email_input\"]\n", "        author, to, subject, email_thread = parse_email(email_input)\n", "        original_email_markdown = format_email_markdown(subject, author, to, email_thread)\n", "        \n", "        # Format tool call for display and prepend the original email\n", "        tool_display = format_for_display(tool_call)\n", "        description = original_email_markdown + tool_display\n", "\n", "        # Configure what actions are allowed in Agent Inbox\n", "        if tool_call[\"name\"] == \"write_email\":\n", "            config = {\n", "                \"allow_ignore\": True,\n", "                \"allow_respond\": True,\n", "                \"allow_edit\": True,\n", "                \"allow_accept\": True,\n", "            }\n", "        elif tool_call[\"name\"] == \"schedule_meeting\":\n", "            config = {\n", "                \"allow_ignore\": True,\n", "                \"allow_respond\": True,\n", "                \"allow_edit\": True,\n", "                \"allow_accept\": True,\n", "            }\n", "        elif tool_call[\"name\"] == \"Question\":\n", "            config = {\n", "                \"allow_ignore\": True,\n", "                \"allow_respond\": True,\n", "                \"allow_edit\": <PERSON><PERSON><PERSON>,\n", "                \"allow_accept\": <PERSON><PERSON><PERSON>,\n", "            }\n", "        else:\n", "            raise ValueError(f\"Invalid tool call: {tool_call['name']}\")\n", "\n", "        # Create the interrupt request\n", "        request = {\n", "            \"action_request\": {\n", "                \"action\": tool_call[\"name\"],\n", "                \"args\": tool_call[\"args\"]\n", "            },\n", "            \"config\": config,\n", "            \"description\": description,\n", "        }\n", "\n", "        # Send to Agent Inbox and wait for response\n", "        response = interrupt([request])[0]\n", "\n", "        # Handle the responses \n", "        if response[\"type\"] == \"accept\":\n", "\n", "            # Execute the tool with original args\n", "            tool = tools_by_name[tool_call[\"name\"]]\n", "            observation = tool.invoke(tool_call[\"args\"])\n", "            result.append({\"role\": \"tool\", \"content\": observation, \"tool_call_id\": tool_call[\"id\"]})\n", "                        \n", "        elif response[\"type\"] == \"edit\":\n", "\n", "            # Tool selection \n", "            tool = tools_by_name[tool_call[\"name\"]]\n", "            \n", "            # Get edited args from Agent Inbox\n", "            edited_args = response[\"args\"][\"args\"]\n", "\n", "            # Update the AI message's tool call with edited content (reference to the message in the state)\n", "            ai_message = state[\"messages\"][-1] # Get the most recent message from the state\n", "            current_id = tool_call[\"id\"] # Store the ID of the tool call being edited\n", "            \n", "            # Create a new list of tool calls by filtering out the one being edited and adding the updated version\n", "            # This avoids modifying the original list directly (immutable approach)\n", "            updated_tool_calls = [tc for tc in ai_message.tool_calls if tc[\"id\"] != current_id] + [\n", "                {\"type\": \"tool_call\", \"name\": tool_call[\"name\"], \"args\": edited_args, \"id\": current_id}\n", "            ]\n", "            \n", "            # Create a new copy of the message with updated tool calls rather than modifying the original\n", "            # This ensures state immutability and prevents side effects in other parts of the code\n", "            # When we update the messages state key (\"messages\": result), the add_messages reducer will\n", "            # overwrite existing messages by id and we take advantage of this here to update the tool calls.\n", "            result.append(ai_message.model_copy(update={\"tool_calls\": updated_tool_calls}))\n", "\n", "            # Update the write_email tool call with the edited content from Agent Inbox\n", "            if tool_call[\"name\"] == \"write_email\":\n", "                \n", "                # Execute the tool with edited args\n", "                observation = tool.invoke(edited_args)\n", "                \n", "                # Add only the tool response message\n", "                result.append({\"role\": \"tool\", \"content\": observation, \"tool_call_id\": current_id})\n", "            \n", "            # Update the schedule_meeting tool call with the edited content from Agent Inbox\n", "            elif tool_call[\"name\"] == \"schedule_meeting\":\n", "                \n", "                \n", "                # Execute the tool with edited args\n", "                observation = tool.invoke(edited_args)\n", "                \n", "                # Add only the tool response message\n", "                result.append({\"role\": \"tool\", \"content\": observation, \"tool_call_id\": current_id})\n", "            \n", "            # Catch all other tool calls\n", "            else:\n", "                raise ValueError(f\"Invalid tool call: {tool_call['name']}\")\n", "\n", "        elif response[\"type\"] == \"ignore\":\n", "            if tool_call[\"name\"] == \"write_email\":\n", "                # Don't execute the tool, and tell the agent how to proceed\n", "                result.append({\"role\": \"tool\", \"content\": \"User ignored this email draft. Ignore this email and end the workflow.\", \"tool_call_id\": tool_call[\"id\"]})\n", "                # Go to END\n", "                goto = END\n", "            elif tool_call[\"name\"] == \"schedule_meeting\":\n", "                # Don't execute the tool, and tell the agent how to proceed\n", "                result.append({\"role\": \"tool\", \"content\": \"User ignored this calendar meeting draft. Ignore this email and end the workflow.\", \"tool_call_id\": tool_call[\"id\"]})\n", "                # Go to END\n", "                goto = END\n", "            elif tool_call[\"name\"] == \"Question\":\n", "                # Don't execute the tool, and tell the agent how to proceed\n", "                result.append({\"role\": \"tool\", \"content\": \"User ignored this question. Ignore this email and end the workflow.\", \"tool_call_id\": tool_call[\"id\"]})\n", "                # Go to END\n", "                goto = END\n", "            else:\n", "                raise ValueError(f\"Invalid tool call: {tool_call['name']}\")\n", "            \n", "        elif response[\"type\"] == \"response\":\n", "            # User provided feedback\n", "            user_feedback = response[\"args\"]\n", "            if tool_call[\"name\"] == \"write_email\":\n", "                # Don't execute the tool, and add a message with the user feedback to incorporate into the email\n", "                result.append({\"role\": \"tool\", \"content\": f\"User gave feedback, which can we incorporate into the email. Feedback: {user_feedback}\", \"tool_call_id\": tool_call[\"id\"]})\n", "            elif tool_call[\"name\"] == \"schedule_meeting\":\n", "                # Don't execute the tool, and add a message with the user feedback to incorporate into the email\n", "                result.append({\"role\": \"tool\", \"content\": f\"User gave feedback, which can we incorporate into the meeting request. Feedback: {user_feedback}\", \"tool_call_id\": tool_call[\"id\"]})\n", "            elif tool_call[\"name\"] == \"Question\": \n", "                # Don't execute the tool, and add a message with the user feedback to incorporate into the email\n", "                result.append({\"role\": \"tool\", \"content\": f\"User answered the question, which can we can use for any follow up actions. Feedback: {user_feedback}\", \"tool_call_id\": tool_call[\"id\"]})\n", "            else:\n", "                raise ValueError(f\"Invalid tool call: {tool_call['name']}\")\n", "\n", "        # Catch all other responses\n", "        else:\n", "            raise ValueError(f\"Invalid response: {response}\")\n", "            \n", "    # Update the state \n", "    update = {\n", "        \"messages\": result,\n", "    }\n", "\n", "    return Command(goto=goto, update=update)"]}, {"cell_type": "markdown", "id": "164b0897", "metadata": {}, "source": ["Now, let's compile the graph. "]}, {"cell_type": "code", "execution_count": 9, "id": "3b6d1013", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28444\\1607184874.py:38: LangGraphDeprecatedSinceV05: `input` is deprecated and will be removed. Please use `input_schema` instead. Deprecated in LangGraph V0.5 to be removed in V2.0.\n", "  StateGraph(State, input=StateInput)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAh4AAAMLCAIAAACOzQmBAAAAAXNSR0IArs4c6QAAIABJREFUeJzs3XdcE/f/B/BPCEnYMwzZIIqAAgoqWrQqbsUiKqioiMU9qoKzBa2jtnVUa12tW7Suur5OVFy4EQVcKDJkK5uwM35/pD+KiggkcJfk9Xz4R5K75N6JwDv3ed3njiESiQgAAID0KFFdAAAAyBu0FgAAkDK0FgAAkDK0FgAAkDK0FgAAkDK0FgAAkDJlqgsABfU+vZJXxC8rFlRXCivLhVSX82UsjhJTmahpKatrKRuacpQ5DKorAqAvBua1QEtKiitNespLflZqaa9eVSlU12TqGrGrK2WgtbBVlIrz+aXF/NJifn52FdeEY+2obtdJU1WTSXVpALSD1gIt5PVj3p2zueZt1UxsVK0d1Tlqsj0Ym55Ynvy09F1ahbGlSvehXIZsvxsAKUNrgWZXUSq8fDCbraLU3YurqStvY7CPrxXeOZvbx8/Ivosm1bUA0AVaCzSv9NflF/dl+cwy1zNmUV1LM7pzNo9fJezpY0B1IQC0gNYCzSg3syrq1HvvGaZUF9IS4m4VFeRUfT0S3QUArQWaTWIsLz6qaPhMhegrYrE3i94mlHpNNqG6EACKIXyEZlH4vvruuTyF6iuEEOee2q2sVe+ez6O6EACKobVAs7h+7J3/Ykuqq6CAW19dIV+UFFdKdSEAVEJrAem7ey7PvK2akqL+cLn00r3+zzuqqwCgkqL+9kOzqaoQxkUVufbVpboQyqhrMW1dNGJvFlJdCABl0FpAyh5fL+w10pDqKij21TCD5GcYEwPFhdYCUvbsbpF5W9WW3GJiYuLQoUOb8MRFixadPn26GSoiTCZRUmK8fVnWHC8OQH9oLSBN79IqNXSU1Vr2tFpPnz5t2hOfPXsm7Vr+Y+2ojh0XUFiY1wLSFBNZyFQmzj11muPFi4qKduzYERUVVVhY6ODgMHjw4GHDhm3ZsmXPnj3iFebNm+fv73/kyJFbt249ffqUw+G4ubnNnDnTxMSEEHLo0KH9+/cvXrx44cKFI0aMOHbsmPhZGhoa169fl3q1ZcWCiIM53tMxxwUUEfZaQJreZ1SoaTbXWcJWrlwZHR29dOnSo0ePOjo6rl69+unTpzNnzpwwYYKxsXF0dLS/v/+jR4/Wrl3bsWPH8PDwjRs35uTkhIaGip/OZrPLysr279+/YsWKMWPG3L59mxASGhraHH2FEKKmxcxMKhfw8dUNFJG8nSsQqFVaLFDTaq7RsJiYmICAAHd3d0LI7NmzPT099fT0PlrHxcXlyJEjVlZWTCaTEDJu3LiQkBAej6ehocFkMsvKymbMmOHm5kYIqaysbKY6a6hrMUuLBVp6+C0DhYMfepCmsmK+ulZz/VC5uLgcOHCgqKjoq6++cnZ2dnBw+HQdJpOZlpa2fv36+Pj48vJy8YP5+fkaGhri23U+q5moaymXFfPRWkABYUAMpImprKTEbK7LLy5fvnzs2LFRUVFTp07t27fv9u3b+Xz+R+tERkaGhIQ4OTnt2rXr4cOHGzdu/GgFNpvdTOV9iqWiJJKBi5wBSB++T4E0sVUYpYXN9T1dS0tr0qRJgYGBsbGxkZGRO3fu1NbWHjNmTO11Tp482bFjx2nTponv8ni85qikgYpyq5tveBCAztBaQJrUtZRLiz/ek5CKwsLCS5cueXt7czgcFxcXFxeXFy9evHjx4qPVioqKzMzMau5eu3atOYppoGYdHgSgMwyIgTRxTTmVFc0yBsRkMrdt27Zo0aK4uLj8/Pxz5869fPnS2dmZEGJhYZGbm3vjxo3U1NS2bds+ePAgJiaGz+eHh4crKysTQrKzsz99QQ6HY2ho+ODBg+jo6E8H1iRXWSYysVFVZjfX8CAAnaG1gDSZWKskRBc3xytrampu2LAhJydn0qRJ/fv3P3DgQEhIiI+PDyHEw8PDxcUlODj40qVLs2bN6tKly9y5c7t165abm7ts2TIHB4cZM2ZcuXLl09ecNGnS/fv3g4ODawJ/KUp6WqKujV0WUFCYMglS9ufSpIlhVmwVRf/Wcn53VrvOWjYd1KkuBIACiv77D1Ln2E077ZX0dwJkTmWF0NoBfQUUFHbYQco6fKV9ckt6a6fP/lX9+eefL168WOcikUjEYNQdTqxcubJHjx7SK/MDffv2/VzcUk9Jx48f53K5dS56cCnf1EaVgaPDQFFhQAyk79qxdwamnPbdtetcWlhYWFZW9ymBKysrORxOnYv09PRUVFSkWuZ/MjMzP7eonpKMjIzEc/4/IuCLdixJmrG2tVRrBJAlaC0gfRVlwsvh2V5TFPTMjNFXClTVmY7dtKguBIAyyFpA+lTUlDr21j21NYPqQijwMrqkIKcKfQUUHFoLNAuzNqrW7dUjDuZQXUiLSntV9uR6QT9/I6oLAaAYBsSgGaW8KHsdw+vnrxDXM05+Vhp3q+ibaQo6DAhQG/ZaoBlZ2auZ2qoc2ZBWXSnnp2mMu1X07F4x+gqAGPZaoNm9S6u8duydeVu17kP1qa5F+hJjeXfO5tl30ercT5fqWgDoAq0FWsijqwV3z+V17q9n3kbVpLUq1eVIqjifn/yUl/mmgjBI96H62lwW1RUB0AhaC7So2JtFb+JK3mdUOXTREolE6lrK2lyWQCADP4RMFoNXwC8rFpQW8/OzqiorBNbtNexcNQ3N6571AqDI0FqAAlUVwozEipKC6tJivlAoKisWSPf1Hz16ZGdnV3NlSalQUVcihKGuxVTTUjY04+gZt9wlxQBkDloLyCF/f/+wsDA7OzuqCwFQUDhCDAAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBQAApAytBeSQtrY21SUAKDS0FpBDRUVFVJcAoNDQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMrQWgAAQMoYIpGI6hoApMPV1ZXBYAiFQgaDwWAwCCEikcjCwuLkyZNUlwagWLDXAvLDwsKCEKKkpCTuK4QQTU3NwMBAqusCUDhoLSA/+vfv/9Ejpqamw4YNo6gcAMWF1gLyw8/Pz9zcvOauhoaGr68vpRUBKCi0FpAfenp6AwYMqBkNMzEx+eabb6guCkARobWAXPHz8xMnLmw2e+TIkVSXA6Cg0FpArujq6vbr108c6fv4+FBdDoCCUqa6AJAZ1ZWi3KzK4rxqQTWtD1h3bfNNjO27Hj16PL9XTHUtX6CmydQ3UdHUZVJdCICUYV4LNMjz+8UJ0byqKqGxlWoFT0B1OXKivJTPK+QbmHIGjDeiuhYAaUJrgS9LeMRLiCnp7duK6kLkU1JcSfLTEu/pJlQXAiA1yFrgC1JflD27V4y+0nxsnDRtnLTO78mmuhAAqUFrgS94cqOwy0ADqquQc9btNfjVopzUSqoLAZAOtBaoj0hE0l6XaXNZVBci/ziqzLxstBaQE2gtUB9eAV/fmEN1FQpBU1e5tJhPdRUA0oHWAvVikMpyHA/WEgQCIhJSXQSAlKC1AACAlKG1AACAlKG1AACAlKG1AACAlKG1AACAlKG1AACAlKG1AACAlKG1AACAlKG1AACAlKG1AACAlKG1AACAlKG1QIv6ISx44aJZVFcBAM0LrQWkLCkpcfTYoZ9b2uvrfp59BrZsRdJR//sCgNqUqS4A5M2Ll0/rWdrXUyb7yhffFwDUhr0WkKYbN6+uW78qJye7t6fbseMHXycm9PZ0u3cvaqTvwKApYz4aELt799bqn37wHT148NAewSHTnzx5VPM6p88cHzfee5h3nzW/LBO/2rXrl8WL4uOfhCyY4TWsV0DgyG3bN5aWln6xqtCwkJWrlu748/fenm43b0USQh4/if5u3uQhXj2/Ge753bzJd+7cFK958NCeQUM8ap6YmZUhrn/nri213xchJDf3/YqVS/zGDBnm3Wf1mtC0tFTxUz59ywAKCK0FpOnrnp6j/SYYGRlfuxo9aqQ/m8UmhOzcvcXPd3zw/B9qr1lWVrbqp+/5fP6Py9fu2XXM1NT8+9B5hYUFhJBnz+I2bvrZ03PggX0nenzV+8eViwkhTCaTEPL2bcrCxbOq+dVb/ti7LPTn169fBodMEwq/cJ0TFouVkPA8KTlx9coNTh06ZmSmzw+eZm5mufOvw1s279HR1l3248Lc3Pf1vELQtzNrvy8+nz8/ZFr80ychwaF7dx/T0tKeOWtiZlYGIaSetwygONBaoBmJ+8FX3b8eNdLfvp1j7UVqamo7/zo897vF9u0cjYyMp0yeU1ZW9vRpLCHkUsRZfX1uwIQp2to6Hh69XDt1qXnWlasXWMqsFcvXWlhY2djYLlgQlvDqxZ27N79YRm7e+xXL13bv3lNHR/fMmeMGBoZzv1vcytjEzMxiQUgYk8mMuHyu4e8rNi4mLS11yeIVnd3c9fT0Z80I1tTSPnHicP1vGUBxIGuBZte2jX2dj5eVlu7c+UdsXExeXq74kcKiAkJISmqSo4OTktK/33t69OgTfnC3+PbTp7Ht2jlqa+uI77YyNjExMYuNjfH4qlf9NVhaWHM4/16JOfVtsl1bB2Xlf3/4NTQ0LMytkpJeN/wdxcc/YbFYnTp2Ft9lMBguzq7x8Y+/+JYBFARaCzQ79v//Ta8tOzvru3lBnd26hX7/k4NDB6FQOHDwV+JFpaW8Vq1Ma9bU1+PW3ObxSsRhRu2XKijIa1QN+Xm5FhZWtZeqqKqWlZc1/B3xeCXV1dUflaGv/1+ddb5lAMWB1gLUiLx2qbq6etHC5SoqKoSQmh0XQgiHoyLg82vu5uX/t0hPn9tBVTVw4rTaL6WtpdOoTaupq1dUVtR+pLyszNLC+tM1hQJBna+gr89VVVVdveq32g8qM/HbBPAv/DIANYqKCjU1tcR9RXxoWc2iVsYmKalJNXdv375ec7u1TZtr1yJcnF0ZDIb4kZSUJDMzi0Zt2q6tw+Ur5/l8vnhMrLikOPVt8sCBwwghbDa7qqqqZlFqanKdr2Bj06a8vNzY2KSVsYn4kYzMdD1d/UaVASDHEOODlJmZWeTl5d6+faPmeNw62bZum5eXe+78KT6ff+/+7fj4x1pa2u/eZRNCunXr+ebN6yNHD4hEoofR9+Ljn9Q8y9d3PF/A/2Pr+oqKirdvU7bv2DQpyC855U2jKhw6ZHhJSfGG337KyclOSUla83OYqqraoIHDCCGOjs5CofDylfOEkJyc7MNH99f5vrp26d6lS/e1a1fk5GQXFRWeOHlk+owJFy6eadIHBiCH0FpAyty7enRo7/JDWPDVyEv1rNa37yD/sYF79m7vN8D95Kkjs2ct6N9vyIHwXZt+/6VP7/7DvX137toyfES/k6eOTJ48mxDCUmYRQrS1tHftPKLCUZk6fVxA4MjYuJhFC5a1sbVrVIXm5pbLwn5+8+bV6LFD5wVPZTAYmzftUlNTI4Q42LefPm3utm2/9fZ0W7FqybeBMwghAoHg0/e1ZvXGnj09V6xa4u3T99TpowMHePkM95P4wwOQEwyRSER1DUBfJQX8fzanj/jOqgHrSg2fz09JSbK1bSu+++LlsxkzA3bvPGJt3boly2hhT67nc1RIlwF6VBcCIAXYawHaefwkevLUsb9v/jU7O+v58/hNm37u0MFFvvsKgJxBjA+009nNfd7cJZcizk4K8tXQ0HRzdZ82bW79T/H26Vv7oLLali5Z2a1bj+apFADqhtYCdDTMa8QwrxENX3/b1v2fW6SrgyEmgJaG1gLyoOYgYACgA2QtAAAgZWgtAHSRmZlZXl5OdRUAUoDWAkAXjx49Gj58OCGEx+MdPXo0ISGB6ooAmgitBYAuvLy8Ll68KL7ATGpq6l9//UUISU9PX7Nmza1bt6iuDqAR0FoAaIfD4SxYsGDdunWEEAMDAzs7u9evXxNCoqOjg4KCTp8+Lb6WGtVlAnwWWgsArXE4HB8fn0mTJhFCXF1dZ82apaOjQwiJiYnp3bv3oUOHCCFpaWnZ2dlUVwrwH7QWAJnBYDBcXFy+/vprQoiHh8eZM2fc3d0JIampqUFBQbt37xbv2dy5c6eyspLqYkGhYV4LgKzS1NTU1NQUt5mzZ8+Kjy4TiURHjhzJzs728fE5ceIEj8cbMmSIvj5O+A8tCq0FQE6oqqoSQjp37ty5879XVra3t798+fKbN2/09fXXrl1bWlo6ffp0IyMjqisF+YfWAvVRZjHUtfBD0hKUlBgqalIeoLa3t7e3txffnjBhwsOHDysqKgghkyZNqqqqWr9+vZGRUXJysrV1HVfYBJAEshaoj6oGs7SYX1pU95kfQYpyUst0jdjN9/pGRkZDhw61tLQkhOzevTs0NFR8ic8//vjD3d29qKiIEBIREZGaWt8F3AAaCK0F6nPlyhVdy7K0V6VUFyLnqsqF1ZVCM1vVFtuinZ2dtrY2IWT9+vW3b99WV1cnhDx48CA4OFggEPD5/D/++CMqKqrF6gE5g9YCdXj79q34u+2VK1fcB+pnvSlNjudRXZTcEonItaNZfccaMSj6dWQymcrKyoSQH3744fjx40wmU0lJSUND4+rVq4SQnJyc+fPnHzt2rOaCmwBfhKtMwgcyMzOnT5/u7e0dGBgoEAiYTCYhhIjIqe0ZBmaqHFWmrrGKUCCkukw5Uc4TFOdVPbmR77/IUteQRXU5dRMIBLdv387Ozvb19X316lVISMiQIUOmTp1aVFSkrKws3t0B+AhaCxBCyOnTp+/evfvzzz9nZWUJhUJTU9NP10mILslKqaiuFJUUVFNRYyPk5OTo6emxWDT9Y11DTYtpaM7p1FuX6kIaITMzMzs7u1OnTvHx8bNmzfLy8goJCUlOTi4pKWnfvr2SEgZCgKC1KLo7d+44ODjo6OisXLlyxIgRDg4OVFckHf7+/mFhYXZ2dlQXIv9yc3O5XO6zZ8/Wr1/v4OAQEhJy9+7dnJycHj16YDKNIkNrUUTFxcVaWlrBwcF8Pv/nn38Wz4eQJy9evLC0tFRTU6O6EEX06tWro0ePOjg4+Pj4HD16NC0tzdfX19zcnOq6oEWhtSiWuLi4NWvWTJ8+vWfPnjweT0NDg+qKQJ5lZWVdv37d1ta2c+fOK1euTE9PX7Bgga2tbV5eHvZp5Btai0K4ePFibm7uuHHj7t27p6+v36ZNG6oral6rV6+eMGECvinTSlVVVXx8PJfLtbS0XL58eVRU1J9//mljY/PkyRMLCws9PT2qCwRpQuYmz+Lj48UXmIqKiurWrRshxN3dXe77CiHk+fPnOOc83bDZbFdXV/GczeXLlx8/fpzL5RJCLl++PHr06PT0dELI8ePHo6Ojqa4UpAB7LXJIfNDw0KFDnZ2dV69eTXU5FEDWInPEP7R79uy5f//+unXrNDQ0fvzxR3t7e19fX6pLg6ZAa5ErN2/e3Ldv35o1awwNDXNycnAiQpBdFy5cePHixfz583k8XlBQUPfu3efMmVNVVcVmN+PpcEBamMuXL6e6BpDUzZs3c3NzTUxMoqKivLy8WrduTQhR5Ih+9erV1tbW4hOZgIxq06aNeBSXzWa7uLgwmczWrVvn5OR4enqmp6f37t07Pz8/JydHfGE0oBuc1FaGZWZmmpiY7Nq16/nz5yEhIeL5HFQXRQvIWuSMra2tra0tIcTExOTevXvJycmEEB6PFxISYm1tvXbt2jdv3qSmprq6uuL7BE1gQEwmZWZmzpkzZ/DgweKzo2OI4CPIWhRHZWUlh8NJTU3dunVrq1at5s6de+fOnfj4+H79+tnY2FBdneJCa5El//vf/x4+fLhixYr09HQ+n29lZUV1RQC0k52d/b///c/Y2NjLy+vQoUOPHz+eOHGio6OjuAlRXZ2iwMHHMuDhw4fFxcUCgSAmJkZ8wIyZmRn6Sj1WrFiRlpZGdRVADWNj48mTJ3t5eRFCvvnmm8GDB1dXVxNCduzY4ePj8/jxY0JIamoqn4+rEDUj7LXQV2lpqbq6+oIFC3g83m+//Sa+cBM0BM4hBnV6+/Ytk8k0NTXdtm3bvn37duzY4ezsfOvWLUNDQ/y0SBdaCx3Fx8evXbt28uTJPXr0KCwsxDEwjZWQkGBhYSF/50YD6RJ/ezt48OCFCxe+//57e3v73bt3GxoaDho06N/LSUBTobXQSGRkZG5urq+vb1RUlJ6entychxhAVly4cOH+/fuzZ8/W19cPDg42NzefM2cOrhTQBPjIqJeQkEAIiY6OvnTpkouLCyHEw8MDfUUSyFqgaQYNGrR8+XLxqTMDAgK4XK5QKBQIBH379l20aJH4TGh5eXlUlykDMK+FMgKBQElJafjw4fb29mvWrOnUqZObmxvVRcmJhIQEzGsBCTk5OTk5OYlv//PPP+KvgJWVlf7+/mZmZjt37szPz09MTHRyckIO+ikMiFHg9u3b4tOx6OrqZmZmmpmZUV2RvEHWAs1KfFGA3NzcsLAwJpO5efPmN2/e3L9/v1u3btbW1lRXRwtoLS3n7t27ampqzs7Oe/fudXJy6tSpE9UVAYB05OXl7du3T0VFZcaMGVFRUZGRkV5eXh07dqS6LsqgtTS7d+/eGRoa7t2799GjR0uWLDExMaG6Ivm3YsWKwMBAXK8FKMHj8SIjIzkczoABA/7++++LFy8GBQX16NEjPz9fcS5Lgxi/GWVnZ/v7+586dYoQ4ufnt3nzZvSVloGsBSikoaExbNiwAQMGEELGjBmzcOFCXV1d8RX5PDw8rl+/Lj7NXWZmJtWVNiPstUhfRETE/fv3Q0ND3759W15ejqlYLQ9ZC9BTRUVFcXGxoaHhiRMn9u7dO3/+/F69ep0/f15dXb179+4sFovqAqUGR4hJzePHj1u3bq2urn7jxo0RI0YQQiwsLKguSkGhnQM9qaioiA8n8/Hx8fHxEZ+Bhs1mnzlzRldX18nJ6ffff9fQ0Bg9erSsn1wVey3SsXDhwoKCgs2bN+MwRDpA1gIy6smTJ3fv3vX29m7VqtXUqVO5XG5YWJgsnlUTrUVSqampWlpaAoFAfKFvoIPg4OBp06a1adOG6kIAmi47Ozs2NtbDw4MQoq6uTnU5jYMYX1K7du26e/cu+gqtTJkyBbOFQNYZGxsPGDCgoKBg3LhxVNfSaMhaJGVhYaE4BxTKCmQtIDeUlJS0tLSorqLRMCAGcghZCwC1MCAmqdTU1IKCAqqrgA9gXgvIDaFQWFxcTHUVjYbWIilx1kJ1FfCBsLAwHPkN8iEzMzMgIIDqKhoNWYukkLXQELIWkBvIWgDoYvny5ZMmTcKOCwBVMCAmKWQtNPT69evy8nKqqwCQAmQtCgpZCw39+OOPlpaWVFcBIAXIWhQUshYasrW1pboEAOlA1gJAF8haAKiFATFJpaSk5OXlUV0FfABZC8gNZC0Kavfu3ffv36e6CvgAshaQG8haFJSVlRWyFrpB1gJyA1kLAF0gawGgFgbEJIWshYaQtYDckNGsBXstTdSvXz82m81gMAoKCjgcjqqqKoPBYLFYJ0+epLo0IImJiWZmZrjiJ8iB9PT02bNny9wfFmQtTaSjo5OcnCy+XVlZWVxcLBQKhw0bRnVdQJC1gDyR0awFA2JNNHr0aDabXfuRVq1ayeKBHHJp+fLlb9++pboKACkwMTHZt28f1VU0GlpLE40YMeKjK0117tzZ2tqauorgP8haQG4IBILCwkKqq2g0tJam8/X15XA44ttGRkbYZaEPzGsBuZGVlRUYGEh1FY2G1tJ0I0aMqDm8FbsstGJra4sMH+QDk8nU0dGhuopGQ2uRyMiRI9lstomJyYQJE6iuBf6DrAXkRqtWrfbs2UN1FY325SPEREKSm1FZWsJvkXpkjKv9QHvLh3Z2dkoVRinPS6kuh3YYDIaOAUtbn0UYLbpdZC0gNwQCQUlJicztuHxhXsvtM7nP7hVrc1kq6jhMGRpNXUs5802Zho5yBw/tNi4aLbbdpKQkExMTjImBHJDDeS0R4Tkauuwxi2xasB6QQwK+6PqxbCIibTq2UHexscEPLcgJGc1aPrvXcvXwO01djr27douXBPLp6t+Z7btp2zqrt8C2QkNDg4KCcJAYAFXqjvHfp1eVlwrRV0CKengbx91qocPzk5KSKioqWmZbAM1Krua15GZWKLNaNngFecdWVcrPqSrnCVpgWytXrsQuC8gHuZrXwisS6BpxWrwYkHPGlqpFudUtsCEbGxtk+CAfZDRrqbu1CPmi6kphixcDcq6spQ5hDw0NTU1NbZltATQrGZ3XgimTIIeQtYDckKusBUCmIWsBuSGjWQsmQoIcwrwWkBtylbUAyDRkLSA3kLUA0AWyFpAbyFoA6AJZC8gNZC0AdIGsBeQGshYAukDWAnIDWQsAXSBrAbkhEAhyc3OprqLR0FpADiFrAbmRlZU1efJkqqtoNGQtIIeQtYDcYDKZXC6X6ioarSX2Wl69ftnb0+3Zs7gW2Ba1G20mZ8+d7O3pxuc31wm41m9YHTRljPi2t0/f/Qd2NtOGWgyyFpAbrVq1+uuvv6iuotGk1lpOnDyy5pdldS7S1+NOGB/E5RpKa1sN0cCNJiUljh47tKWK+lg9HxpIAlkLyA1Fz1peJjz73CJ9fW7gxGlGRsbS2lZDNHCjL14+bamK6lDPhwaS+Omnn5C1gHyQ0axFOq1l9nffXr58PiLiXG9Pt1evXx7/59BI34FRt6979uuyecu62mNTPB5vz97t02dMGDTEw3+897btG2u+XQoEgt82rhkxasCYsV579m6/dy+qt6dbYWEBIYTP52/bvjEgcOTgoT0WLZlz717UF0uMW2LbAAAgAElEQVSqvdF//vl7xKgBz57FBQSO7O3p9u3k0ZcunSWE7Ny1Zd36VTk52b093Y4dP0gIyc19v2LlEr8xQ4Z591m9JjQt7d9BldeJCb093e7dixrpO1A8duQ1rNeJE4e/mze5t6dbcUnxwkWzlnw/t2br5y+c7u3pVllZSQhZvPS7H1cs3r1n24BB3fsNcJ82fXxi4qtPP7QvvqP3ue9mzJrY29NtwsQR586fEj9Yz+cZGhayYuWSi5f+N+yb3v0GuM+dP+XFy387WVlZ2feh8wcP7TFzduDlKxc+t8X4+CchC2Z4DesVEDhy2/aNpaWl4sdr//+GH9z9xcpbnqWlJa7XAvJBobOWzZt22du3799/yLWr0W3btGOx2OXlZYeP7F+yeMXwb3xrr3n8n0OH/t47enTAofAzs2eGXI28GH5wl3jRkaMHzp0/9d2cRdu3hzOZyjt3byGEKDGZhJDfNq45cfLwCJ8xfx8627NHn2U/Lrx5K7Lh5bHY7JKS4s1/rF20YFnklYc9PPqsXb/y/ft3Qd/OHO03wcjI+NrV6FEj/fl8/vyQafFPn4QEh+7dfUxLS3vmrImZWRmEEDaLTQjZuXuLn+/44Pk/iF/zxMnDtrZ2a3/doqaqVs/W2Sx2zOOHysqsSxfu7N1zXEdXL2xZiEgk+uhD+8JbYLF+3/xrwIQpG9Zvt7Nz2Ljp53fvcur/PNlsdnT0vbt3b23fHn7hXBSbxf7l1+XiRevWr0xPf7tu7baVP65LTEx4GH330y2+fZuycPGsan71lj/2Lgv9+fXrl8Eh04RCISGk9v9vX89BDf+PaDFLly5NSUmhugoAKVD0rKU2JpNZVlb27aQZfT0HmplZ1F402m/Czj///rqnp66unru7R6+v+z18+O/ftUsRZ3v26NOzRx9tLe0J44PU1NTFj1dUVERcPjd2zMRhXiO0tbSHDPbu03tAePiuhtejpKRUXV09c0awg0MHBoPRv/8QgUDw6tWLj1aLjYtJS0tdsnhFZzd3PT39WTOCNbW0T5w4LH5HhJCvun89aqS/fTvHf79KGBjOnhni5tpVWbm+A+0YDEZVVeXYMRMJIaYmZpMCp2dlZz59Gtvw+gkh1dXV3t/4du3SvaOL28SAqXw+//mL+Po/TyUlJULIooXLTVqZKisr9+rVLzU1uaysLDf3/bXrl8eMDnCwb6+npz9t6ncsFvvTLV65eoGlzFqxfK2FhZWNje2CBWEJr17cuXvzo/9fY+NWjXojLSM1NVW8ywgg6xQ9a/mUXVuHTx9ksVgPHt6ZPjOg3wD33p5u/5z4O78gTzzk9fZtiqOjc82aPTx6i2+8fPmMz+d3dutWs6iji9vrxISa8ZkGatfOUXxDQ0OTEMLjlXy0Qnz8ExaL1aljZ/FdBoPh4uwaH/+4ZoW2bexrr//R3XpYW9vWtB8zUwtCSFJyYqOKJ4Q4O3US39DU1CKEVFZU1PN5iplbWKmpqdV+1yUlxVlZGYQQS0ubmrdp17aON/L0aWy7do7a2v+eYaKVsYmJiVlsbEzNCnX+/9IEshaQG1lZWWFhYVRX0WjNOK+Fza7ju/DW7b9dvnx+yuTZnd26GRkZ7/jz9ytXLxBCSstKCSGqqqo1a+rq6otv8EpLxMnERy+Vn5+rrq7e8HoYDEb9K/B4JdXV1b093Wo/qK//3ygnm8P54huskwrnv3F/cQZQXl7WwOfWqHPf6HOfp5h4x+UjRcWFhBANdY1aJal+uhqPVyJOmGo/WFCrbzX87bc89BWQG0wms7q6muoqGq1Fp0wKhcLz50/5jho3dMhw8SM1uw6qKqriXb+alWv+iunpcQkhwfO/NzU1r/1qUj+aWV+fq6qqunrVb7UfVGY25SMSZxI1Skt5NbfFMbtqvfFMw7fyuc+zHtpaOoSQ2uNFZWV17P/p6XM7qKoGTpz26XPpb+nSpVOmTLGysqK6EABJyWjW0qKtpaqqqqKiQl/foObu3Xu3xDsTbDZbX5+bkppUs/LtOzfEN8zNLdlsNpPJ7Ojy7zfo/Pw8BoNRexdHKmxs2pSXlxsbm7QyNhE/kpGZrvf/O0/1Y3M4tf+sv337QYb8Jul1UVGheHBJnPHYWNtKXnA9n2c9jI1NCCHPnsfZ2rYVpzgxjx9yuQYfrdbaps21axEuzq41L5iSkvRRckZbyFpAbggEgoKCApk7SExqWYupqXlCwvPHT6ILCvI/t46KioqpqfnFS//LyEwvKir8dd2Kji5uxcVF4i/y3bv1vHjxTMzjh0Kh8NjxgyUlxeJnaWpoTgyYunffjvj4J1VVVddvXFmwaOam33+RStlmZhZ5ebm3b99IS0vt2qV7ly7d165dkZOTXVRUeOLkkekzJly4eKYhr+Po4PTy5bOUlCRCSPSj+zV9UUxbW+ePLetKeCVFxUV79+9oZWzSvr1zAz+0etT/eX6OgYFh+/bOu3ZvTc9Iq6ysXLlqaZ3jZr6+4/kC/h9b11dUVLx9m7J9x6ZJQX7JKW+aUGfLQ9YCckOh57UQQryG+IhEopAFM94kva5ntbDQNSwWa2LgyHHjvTu7uk+aNIPNYg/z7v3uXU7gxGnt27sEh0yfEOCTlpY6aqR/zVG/Y0YHhASHHjq81+ubXr9v/tXUxHxBiHRyLfeuHh3au/wQFnw18hIhZM3qjT17eq5YtcTbp++p00cHDvDyGe7XkNcZ7u3Xp/eAoCljenu6Xbhwevy4b2uP77W2aWNmZjnKd6D3cM/373JW/LhOvCvQwA+tHvV8nvU8a8niFe3sHCZPGTPEq6eWlvbAAV4fjeARQrS1tHftPKLCUZk6fVxA4MjYuJhFC5a1sbVrWp0tDPNaQG7I6LwWhkgk+vTR+xfyq6uJ89d6LVlKRUXFu3fZFhb/jo8fPrL/8JH9p05cackamsOy5Qt5vJL167ZRXQj1LuxO7zmca2zV7H/0kbUAUItGJ9U/9PeeKdP8T50+VlRUGHkt4uix8GFeI6guCmQSshaQGzI6r4VGJ9UPnDitqKjwwoXT23dsNDAwGu7t5z+2vitCP3sWt3jJnM8t/fvQWQ0Njc8tpZvQsJAnT6LrXDRs2MjJQbNavCLZ9tNPPxkZGVFdBYAUZGVlzZ49++TJk1QX0jg0ai0MBmPe3CUNX9/R0enPPw99bil9+sqPy3/94jpzv1tcVV1V56KasxJAwyHDB7mhrKxsaNiip42XChq1liaoOUpY1tWemAmSQ9YCcsPY2HjHjh1UV9FoNMpaAKQFWQvIDT6f/+7dO6qraDS0FpBDmNcCciM7O3vq1KlUV9Fosj0gBlAn9BWQGzKatWCvBeTQ4sWLcb0WkA/IWgDoIi0tDVkLyAdkLQB08csvv+DwMJAPyFoA6MLMzIzqEgCkA1kLAF0gawG5gawFgC6QtYDckKushaOupMxC1wEp09BhMVlfuFKZVCBrAbkho1lL3f1Dh8vOTm30xdsB6pcUX6LVoIt2SsrMzIzD4bTElgCamVxlLeZtVavKhaSOK7kANFFOaoWpHenj2Wfjxo3iMas3b5rrmpXIWkBuyFXWwlRmuA/SiwjPbPF6QD6Vlwhu/pPlPdn29u3bo0ePJoTk5+cvXbr0t99+I4TExMTcvHmzvLxcWptD1gJyQ0azlrqvMimWmVRxcV9Whx56ekYcFQ1myxYG8oDBYBS9ryotqo67lT9uiSVb5eOvMtXV1SwWKy4ubt++fa6urmPHjj137lxRUdHAgQP19Jp+kdP09HQDAwOMiYEcSE9Pl8XrtdTXWgghpUWCJzcKc95WlBbxW7AqWVJeXq6srMxisaguhI60uWyGksjURq2Tp04Dn5KQkHD+/HlXV9eePXtu3bqVx+NNnDhRFseaAaQiOzt72bJlMjcm9oXWAl8UFhbm7u4+ePBgqguRQxkZGbdv33Z2drazs5s/f75AIAgNDeVyuSUlJZqamvU8cfHixdOmTcNBYgBUwWx8SY0YMYLLxYW8moWpqamvr6/49qpVq548eSL+JvTdd9/l5+fv27dPW1v7xYsX9vb2Hz0RWQvIDT6fn5+fL3M77thrAZmUkZHB5XI5HM7MmTMfPHhw7949Qsjly5ednJxMTEyQtYDckNGsBfMiJXX8+PHY2Fiqq1A4pqam4s6xZcuWhw8fMplMQkhUVNSSJUsIIWpqagcOHIiLi6O6TABJKSsrGxsbU11Fo2GvRVLIWmho4cKFurq6IpFo6dKlr169OnToUJ8+fXr27El1XQCKAnstkhoxYoSzszPVVcAHMjIyfHx8li5dKr7ipJubW3p6OiHk9u3bQUFBFy5cEB/aR3WZAF/G5/Ozs7OprqLRsNcCcuhzWYtIJIqNjeXxeB4eHhERERs2bJgxY8awYcOysrL09PSQzQANIWtRUMhaaOhz5xBjMBguLi4eHh6EkP79+4eHhzs4OBBCHj9+7OnpeebMGUJIbGxsQkICFVUD1EFGsxa0FknFxcVlZGRQXQV8YMGCBQ05hxiXy7W1tSWEDB48OCoqqkePHoSQnJycFStWnD9/nhBy/vz5yMjIqqqqFqkaoA7Gxsbbtm2juopGw7wWSY0aNQrzWugmMzOzCfNadHV1xXsz/fv3FwqFhBANDY1z585pa2u7urpu27ZNXV191KhRqqqqzVM1QB34fH5ubq7M7bggawE5lJmZyeVy2Wy2FF8zJibm9u3b3t7e5ubm8+fPNzAwmD9/PuIZaG7IWhTUsWPHnjx5QnUV8AETExPp9hVCSKdOnWbPnm1ubk4ImTNnjp2dHZ/PJ4QMHDhw5syZhBCBQFBQUCDdjQIga1FQ8fHxmZm4+gC9NDBraTIrKysfHx91dXVCyIkTJwICAsQDF76+vhMnTiSE8Hi8+Pj45isAFAeyFgWFrIWGmpa1NI2amlqXLl0IIRwO5/Llyzk5OeI2s2HDhurq6vDw8IyMjLi4uM6dO+PnBJoAWQsAXTRH1tJkubm5mzZtYrPZoaGhT548uXfvnqenZ5s2baiuC2QDshYFhayFhpoja2kyLpe7cuXK0NBQQoiFhYWysvLDhw/FRzb/8MMPONEZ1A9Zi4JC1kJDzZ21NJmenl5QUNDYsWMJIV9//bWHh0dhYSEhZNu2bYGBgY8ePSKEiB8BEJPRrAWtRVKjRo3q2LEj1VXAB1oya2kydXX1gQMHik+aOX369Pnz52toaBBCwsPD+/Xr9/jxY0LIq1evSktLqa4UqIRziAHQBa2yliYoKCjg8/kGBgbbtm07fPjw1q1bHR0dIyMjTU1N7ezsqK4OWhSyFgWFrIWGaJW1NIGurq6BgYF4b+bGjRutW7cmhKSmpq5YsSI1NZUQsnv37sjISHwvVATIWhQUshYaom3W0jQqKiqEkMDAwIMHD1pYWBBCtLW1L126VFZWRghZtGjRvn37qK4RmguyFgWFrIWGZCJraRoGgyG+StAvv/winrM5ePBgcY/h8Xh+fn6bNm0ihOCUmnKDz+eLrzYkW5C1gByS9aylyd68efPmzZv+/ftnZmaOGjVqyJAhS5cuLSwsrK6uFo+wgcxB1qKgkLXQkKxnLU3WunXr/v37iz+ByMhIHx8fQkheXt6ECRN+/PFHcWDz4sULqsuERlBWVjYzM6O6ikZDa5FUZmZmXl4e1VXAB3766Sd5ylqahsPhtGvXTtxvLly4MHfuXEJIZWXlTz/9hC9DMsTY2Hjz5s1UV9FoOIeYpPr06YNzQ9HNs2fP5DVraTJtbW1CSNu2bbds2aKYu3QySjyvReZ2XJC1gBxS2KwF5A+yFgV1+PBh8cRpoA+FzVoaYv369deuXaO6CmgoZC0K6vnz51lZWVRXAR8IDg5OTk6mugqaKioqKi8vp7oKaChkLQrKz89PX1+f6irgA9nZ2ZjY8TkhISHYpZMhyFoA6CI7O1tfX5/FYlFdCICkkLUoKGQtNGRsbIy+8jnIWmQLshYFhayFhpC11ANZi2xB1qKgkLXQELKWeixcuBC7dDIEWQsAXSBrAbmBrEVBIWuhIWQt9Vi7du3Vq1eprgIaClmLgkLWQkPIWupRUlKCs+DIEGQtCgpZCw0ha6kHshbZgqwFgC6QtYDcQNaioJC10BCylnoga5EtyFoUFLIWGkLWUg9kLbIFWYuCQtZCQ8ha6oGsRbZUV1dnZWVZWFhQXUjjIGsBOYSsBeQGshYFhayFhpC11ANZi2xhsVgyt8uC1iIFyFpoCFlLPZC1yBYjI6NNmzZRXUWjIWuRFLIWGkLWUg9kLbIFWQsAXeTm5mpra+MPKMgBZC0K6tChQ48ePaK6CvgAl8tFX/kcZC2yRUazFgyISerly5c6OjpUVwEfmDt37pw5c2xsbJrwXB4vTSCQ5ygiPz+zoIBbVJRIdSHQICoqZMWK2bT9/1JR0eNw9D59HK1FUmPHjtXV1aW6CviAiooKn89v2nPv3AkRCMqUlOT26vHu7kImM+XMmZOGhtixkwEPHpR06aJJdRV1q6wsbN3a19Fx6qeL0Fok1a5dO6pLgI+FhIRoa2s39dmi7t2DtbUtpVsS3Rw5cuHcuZgNGxYpK+OPAH09eBCvrv5owICJVBdSt+fPj34urMdPlaQOHTpkZ2fn6upKdSHwHy6XS3UJdOfnN8jcvFVubqG6uqqSkpK6uirVFUEdmEzm/Pk07Sv1Q4wvqZcvX+bk5FBdBXxg7ty5SUlJVFdBd927uxgbc1ks5cGDp1679oDqcuADs2atIoS4ujpQXUgTobVIauzYsdhloZv3799XV1dTXYVsUFHh3LixXzwJ4cmTl1SXA4QQcuLElXHjvKiuQiJoLZJq166dkZER1VXABzZt2tS0w8MUVp8+XQkhGRk5o0bNq6iQ5wPkaK66ml9VVd2nTxd3d2eqa5EIWoukMK+FhjCvpWmGDPn611+DebyywsLilJQMqsuRMYmJqUOHTpfkFUpLy3r2HM9ms3R0tKRXFzXQWiSFrIWGkLU0mbW1GZerq6amGhKy9uzZ61SXI0uePpVo6olQKLx8+e7du39LryIq4QgxSWFeCw0ha2m4Xr0Cpk3zu3r13uPHLyIj92hpaZw+HXnixOXs7Nz9+08XF/NMTY2+/rozIaSoqGTHjqNRUTGFhSUODq0HD+4xbFgfQsh3361RVeVYWpocOHBGKBS1aWMRGjq9bVsrQkh5ecXWrX/fuhWTk5PbqpVBp04OwcETVVVVCCG9e08MDBzO45Xt3n1CXV2te3eXkJBAfX0dQkhSUtqOHUejo58xmUpOTnbjx3s5O7cTXyX+jz8ORUXF5OTkdexo7+s7wMPjyzHnrVuPLl2Kiol5UVJS2r69bVDQSFdXR/Gi48cvhYefLS7m9ejhOn2639ChM9asmdevX3dCyJMnL/7889jz52+4XF0Pj06TJ49UV1cjhISErGWxlLt3d9mwYV95eaWTU9vvvhvv6Gi7ZcuhPXtOEkLc3EYtWTJ5xIj+jfpfuHz5Ts+ebt7enk39b6Qd7LVIClkLDSFraTg2m3X48AU7O+stW0LV1FTOn7+5cuU2B4fWZ878ERwcePDguVOnrvbqFVBWVrFy5fbo6GdLl045enS9o6Pt6tV/Pn36mhDCZis/fPiUxVK+c+fQ8eO/6enphISsFR8X8Msvuy5duj1/fkBExM5p0/wiIu78/nu4eLscDnvPnpMqKpxr1/YeP/7b48cv/vrrGCGkqqpq2rQfBQLhjh3LNm/+XkmJMX/+r5WVVYSQNWv+Onz4wpgxg8+e3dqnT9eFC9dHRt6r/92VlZV///0mPl+wdm3IsWMbzM1bzZv3S0FBESEkLi7h5593DhzoceLEpt69uyxe/BshhMlUIoSkpGTMmrW6upq/d+9PP/887+XL5GnTfhQKheKP69692Fu3HoWH/xIVdYDNZi1fvoUQMnPm2AkTvjE25kZHH2tsX0lISI6MvM/hyNUsXbQWSSFroSFkLQ3HZDINDfVCQgK7dnVSVlY+ceJyx472ixYF6enpdO3qNH263507Tw4e/LW6ujo6+mn79rbu7s7GxgazZ/vv2bOKy9UhhDAYjMrKqokTvQkhZmbG06f7ZWa+i419WVzMu3gxasqUUT17umlqqvfv/9Xo0YPOnbspPlECg8FwcGg9aZKPpqa6gYFe165O4gGl1NSs/PyiiRO9bW0t7eys16yZ98sv8/l8fkVF5blzNydO9B4xor+2tqa3t+eAAV/t2nWi/nenpqZ6+PC6xYuDHB1tjY0N5szxLysrj41NIIScPXuDy9WdMmWUjo5Wr15dunTpUPOsCxdusVjKa9eGWFmZ2tpahoVNf/Ei6ebNaEKIkhKDELJ8+UxTUyNlZeV+/bonJ6eXlZU3+fOvqKgsLS1fs2Zek1+BntBaJIWshYaQtTSKvf2/e3h8Pj8+/nW3bi41izp3bi8QCF6/TtXW1nRxsT9//tbUqcsfPXrG5/MdHGyNjQ3Eq9naWtTM6rewaEUISUx8+/ZtFp/P79Chbc2rOTi0Lisrz8h499F2CSGamuo8Xpn46bq6WsuXb/n773PPnycymUw3t/bq6mrPniXy+fxu3f47bsrNrX1CQnJpaVn97660tPzXX3cPHDjFzW1Ur14TCSEFBcXiYTcnp7ZKSv/+DRQfIycWG5vg6Ghbk6WbmBiamRnHxDwX37WyMlVTU/3/stUIIcXFvMZ/6oQQEha2mcFgdOokq5NX6oGsRVLIWmgIWUujsNn/7uFVVFQKBIKtW//euvWDMDk/v4gQsmLFrOPHI86cuTZ16nIOh+3nN2jGjNHijqKiwqlZWXy7rKwiN7fgo0VqairiQSrxXQaD8WkxHA77r79WnDp1ddeuE4WFxebmxlOn+g4c2KOkpJQQ8u23oR+tn5tbKE5B6pSV9T4oKKxbN+effprboUMboVD01Vf+4kU8Xpmp6X9D2Vzuf7/FJSWlCQnJbm6jar9UXl6R+EZNN5LQzZvR7u7OcjYOVgOtRVI4hxgNbdq0SYJziCkuDQ11FRWOl1cvT0/32o+bmxsTQrS0NCZN8gkMHB4b+/LIkQv795/W0lKfOHG4+M90zcriaTFqaioaGmriJL9mUWlpOSHEwKCOE+XWZmVlOnfuhGnT/O7di/3f/67/8MPvNjbm4j/9338/VVxMDUPD+l7t0qWo6urq5ctnijucuNuJqahw+HxBzd3ai7hcXVVVzrRpfrVfSkdHmueIzMsrtLe3+eJHIbvQWiSFc4jREM4h1mRt2liWl1e6ubUX362qqsrKyjUy4hYWFl+6dNvb25PDYbu42Lu42Ofmhr16lZqRkZOZ+e7167eFhcXiEaQXL5LEQ2Q2NuZMJjM2NqFdu38Hvp4+TdTR0RIfBvY5ycnpT5++9vLqraLC6dWri4dHp+7d/Z8/f+Pp6c5ms5hMpZra8vIKGQwiPt7scwoLS7S0NGr2nK5e/S/2NzExTEpKq7l7/frDWh+CRUTEHVdXx5r9qqSkNPFAn+QEAoGn56SrV3czmUypvCA9IWuRFLIWGkLW0mRz5oy7evXe6dORQqHw8eMXS5ZsnD59RWVlFZPJ3Lbt8KJF6+PiEvLzC8+du/HyZbKrq4OJiWFxcSmbzVq3bk9JSan4AGUTE0Nn53ZaWhoDB3rs3Hn85s3okpLSc+duHDlywd9/aJ3jYDUKCop//HHrxo3709Ozk5LS9uw5KRQKnZzaamqqT53qu2PHsSdPXlRVVV25cnfmzJW//LKr/rfTtq1Vbm7BqVNX+Xz+7dsxjx+/0NbWzM7OJYT07On6+nXqgQNnRCLRvXuxtU9yM378MD5fsH793oqKypSUjE2bDvj5Bb95k1bvpoiFRavc3MIbNx6mp2d/bh0+nx8Rcefs2W3y3Vew1yIF/v7+yFroBllLk3XsaB8e/suePSd//z28vLzCycluw4aFHA6bw2Fv2LBo7drdkyb9IN4pCQkJHDasN4PBaNfOurCwxNLSpG/fbwUCgamp0bp1C8T9Y8GCwN9+Yy5dupHPF5ibGwcFjRg/flj9BXTq5LB06ZQdO46Gh/+PEOLu7rxjx3IbG3NCSECAt52d9d69px48iNfQUHN2tgsL+8Ls90GDeiQnp2/ffmTVqu3du3dctmzGvn2ndu36p7iYt2DBpLi4V1u2/L1v3+kOHdrMnu0fELBEfGChtrbmkSPr9+07NW7copSUDEdH22XLZtjZWde/LQ+PTi4u7YKDf/3uu/F1vs0HD+KtrU0HDerRgP8HmccQfe50+wAyKz8/X0tLq2lXIomI8O3adYbcX69FihYuXFdSUrpt27KkpPTvv9+4Y8dyDQ01aWXdzYfP5yclpYundhJCnj1LDAhYcuTI+tatpXO14FGj5h47trHmbmbmu1Wrtm/dGiaVF6eJ58+PikQ6dV4KjO7//fQXHh4eHR1NdRXwAT09PVzhquXZ2Jj9/fc6NpuVlZW7bdvhT1dYuvQ3KuqqW3T0s7FjF/z6666srPfx8a9+/vkvF5d20uorjx+/KCkp7dr13wMBiop4798XyFlfqR9+/ST16tUrPT25PcxDRs2ePXvevHmYkE8JFRWOqakhh8Net25PSEhgzePduo1RUWFfuHBz0KCeUtzcgQNndu36p85FtrYWO3eu/NwT3d2dlyyZfPbsDV/f+Zqa6u7uTnPnTpBWVRcu3Hr/voDBYHh4+Pv49AsM9HF2tpPWi8sEDIhJKiEhQVdX19DQkOpC4D/+/v5hYWF2dk35ZcaAmLTw+XxlZeXfftvn5tZ+7drdmZnvCCFmZkb79q3R1pbagbwlJaXiKS+fYrGUKTm6t6ioOCBgaXr6v0f3cDjs27cPtnwZLaCeATHstUiqaX+/oFlt3rxZS0vmT0su68RjkhMmfLNq1XZxXyGEvH2btWzZHxs3LpHWVjQ11TU11aX1alIRGXk/N7ew5m5lZVW/ft9evvyFg9nkDLIWSSFroSFkLfShr57Ct/UAACAASURBVK+TkJBcc1dJSSkm5vmJExGUFtW8zp27+dFZxfLzi/r2nURdRRRAa5HUq1ev3r17R3UV8IHZs2djXgt9ZGW9r323rKxiz56TGRnyORssPv7V27dZSkpKQqFQJBJxOGwjI30np7bydML8hsA3O0lhXgsN5efnY14LTXz1lT+DwRCJRCKRiMFgCIVCJSWl9PScpUs3/r5uTWWFvGW9Jw/HMKr02lhYczhsW1vL9u1tW7e2MDLSJ4S8S6vjzSopMfRbEYbcfclHjA9yCPNaaOXvv89VVlZVVlbxeGVlZRUCgZDL6Mvg2RiZq1RXUV1cM+BX85WYSg2c2aNjwEyKK7d1Zvf0EahqNH9xUoUYvxmFh4e3a9fOzc2N6kLgPzgcnFbGjBny3x0R+d9fDGMbXVtnLWV2fWd8URwe3iQ/uyp8Tfq4xUqqmnLyXV/udsNaHLIWGkLWQlv/20ks7PXbddZGX6lNz5g9eqHN7uV8kZDqUqQErUVS/v7+2GWhG2Qt9JT8lGjoqNs4SfPs9PKk16hWUf+juggpwYCYpDCvhYYwr4We3qWJWBz8zfksLX3Wkxsi8o087M9hr0VSmNdCQ5jXQk/lZUp6RpwGrKigtA3YLDaTyEXagtYiKWQtNISshZ4qSkXV1fISJjQHEXmfXk3kYacFA2ISw7wWGkLWAkAttBZJIWuhIWQtANTCgJikkLXQELIWAGqhtUgKWQsNzZw5882bN1RXAaC48M1OUhMmTNDR0aG6CvhAYWEhn8+nugoAxYXWIilbW1uqS4CPbdmyRUND1s7HBCBHMCAmqf379z98+JDqKuADOjo6yFoAKITWIqnExMT37983YEVoOchaAKiFb3aSQtZCQ8haAKiF1iIpZC00hKwFgFoYEJMUshYaQtYCQC20Fkkha6EhZC0A1EJrkdSECRO6dOlCdRXwAWQtUCdvn76ZWRmNfVZSUuLosUObpyK5hUEDSSFroSFkLfCpjMz0oqLCJjzxxcunzVCOnENrkdT+/fvt7e07d+5MdSHwHxyzp8hEItHxfw5FRJxLz3hraWHt6tp1UuD0mMcPFy6aRQjxH/fNV199vWrF+uTkN2f+d/xRzIN377ItLay9vEYMHTJc/Apew3oFTpx249bVuLjHI0eOPX78ECGkt6fbjOnzRo30p/r9yQa0FkklJiZyuVyqq4APzJw5c/78+a1bt6a6EKDAiROHd+/ZNnfO4s6du925e3Pnri2amlqj/SasWb1xyfdzD4afNmllSgjZ/Mfa97nvgud9b2Vlc+Pm1fUbVhsZters5k4IYbHZJ04e7uruMX5ckIuzqzJT+dr1iMOHzlL9zmQJWoukMK+FhpC1KLLYuBhnZ9cBA4YSQoYOGe7i4lZZUfHpasuW/VJeVmZs3IoQ8s2wkefOnXzw4I64tTCZTK6B4eyZIVSULyfQWiSFrIWGkLUosvbtnf/8a/Ova1d0797T2dnVzNS8ztVEQuGxfw4+eHAnPf2t+BFLS+uapW3b2LdUvfIJrUVSyFpoCPuRimyEzxhVVbU7d2+GhoUoKyv36TNgStBsff0PRq0FAsGixbNFItGUybNdXNw0NTRnzJpYewU2m93ihcsVtBZJIWuhIWQtiozJZHoN9fEa6pOSkvTo0f29+3aUlZauXLGu9joJCc9fvX65ft22Th3//VLI45VQVK98QmuRFLIWGkLWorBEIlFExDk7OwcrKxvxv+KSoksRHyfw4qOQufoG4rtJSYlpaal2bTEIJjWYMikpW1tb7LXQzZYtW7DLopgYDMaliLPLflx49+6t4pLie/eiom5fd3RwIoSYW1gRQm7cuPL8xVMr69YMBuPY8YM8Hi81NXnrtg2d3dyzc7LqfE0zM4u8vNzbt2+kpaW2+BuSVWgtktq7d++DBw+orgI+gHOIKbJFC5dbmFst/WHeN9591m1Y1cOj9/x53xNCTE3MBg7w2r1n219/bW5lbPL90lXxT594fdPrh7Dgb7+dOWzYyKdPYycF+X36gu5dPTq0d/khLPhq5CUq3pBMYohEIqprkG1hYWHu7u6DBw+muhD4z/Tp04ODg5t28F5EhG/XrjO0tS2boS5Fd3E/aWXDtemgSXUhNCUSkgOrEmeuZ1JdSEM9f35UJNJxdJz66SJ8s5NUQECAtrY21VXAB4qLiwUCAdVVACgutBZJYUyfhrZv366mpkZ1FSCRoqLCceO961ykoanFKymuc5G1je3vG3c2U0lHjh4ID99V5yIlJlP4mW8zQUGzvhk2splKoi20Fknt3bvXwcEBJz+mFU1NDLnIPA0NzT//PFTnoqrKSjaHU+ciljKr+UoaPNi7Z0/POhfxSko0PvNTp6mh1Xwl0RZai6SSkpIMDQ2prgI+IEnWAjTBZDJbGZtQXcUHNDU0NTU+863FuKWLoTm0Fkkha6EhZC0A1EJrkRSyFhpC1gJALcxrkRTmtdCQpqYmkykzR3ACyB+0FkklJSXl5uZSXQV8YPr06YmJiVRXAaC4MCAmKWQtNISsBYBaaC2SQtZCQ8haAKiFATFJIWuhIWQtANRCa5EUshYaQtYCQC0MiEkKWQsNIWsBoBZai6SQtdAQshZ60tAmTGWMlHyeiLSyZhEipLoOKcB/s6SQtdAQshZ6UtMUvU8vo7oK+srNqhDw5WRvG61FUshaaAhZCz2Z2jIqSquproK+8rMqbTowqK5COtBaJBUQEIDTHtMNshZ6MrIgOtzKO//LoboQOkp/VZbwKM+17hMryx5kLZJC1kJDf/31F+czJ10HanUbQp5cL7/5T4aVow7XVEWZJSdf0iWRn11ZlFuR8LDAf7H8fBpoLZLavXu3g4ODu7s71YXAf5Dh05lLL5GOYeWzeznP7ykV5FC5cykSiYRCEZNJ5eCNoQVTyBdaORB56itoLVKQkpJibIxrNdDL5MmTFy1ahOu10JaVA8PKgRAionZMPiLi9vXrD3/6aS6FNRAiIkSumooYWoukJk2ahGsa0k1ZWRmyFgAKobVIysrKiuoS4GPIWgCohSPEJLV79+579+5RXQV8QE1NDfNaACiE1iKplJSU/Px8qquAD0yePBnzWgAohAExSSFroSFkLQDUQmuRFLIWGkLWAkAtDIhJClkLDSFrAaAWWoukkLXQELIWAGphQExSyFpoCFkLALXQWiSFrIWGkLUAUAsDYpJC1kJDyFoAqIXWIilkLTSErAWAWhgQkxSyFhpC1gJALbQWSSFroSFkLQDUwoCYpJC10BCyFgBqobVIClkLDSFrAaAWBsQkhayFhpC1AFALrUVSyFpoaNeuXWw2m+oqABQXBsQktXPnTmQtdKOioqKkhJ9tAMrg109Sb9++RdZCN99++y2yFgAKYUBMUt9++62WlhbVVcAHKioqkLUAUAitRVKWlpZUlwAfQ9YCQC0MiEkKWQsNIWsBoBZ+/SRVWlrK4/GorgI+sGzZMmQt8EVsNsvAQJfqKuQTBsQk5e3tjayFbhITE5G1wBdVVVW/f19AdRXyCa1FUshaaAhZCwC1MCAmKWQtNISsBYBa+PWTFOa10BDmtQBQCwNiksK8FhrCvBYAaqG1SApZCw0hawGgFgbEJIWshYaQtQBQC79+kkLWQkPIWgCohQExSSFroSFkLQDUQmuRFLIWGkLWAkAtDIhJClkLDSFrAaAWfv0khayFhgIDA1+/fk11FQCKCwNikpoyZYqGhgbVVcAHqqqqhEIh1VUAKC60FkmZmZlRXQJ8bO/evcrK+NkGoAwGxCT1559/3r17l+oq4AMsFovBYFBdBYDiQmuRVHp6ekEBzstNL8haAKiFQQNJIWuhIWQtUI+AgMUCgUggEBQXl5SWVowbt0ggEJSXV546tZnq0uQHWoukkLXQELIWqIeREffKlbs1h6e/fJlECDEzM6a6LrmCATFJIWuhIWQtUI9x47y43A+uWywSiQYN8qCuIjmE1iIpZC00hKwF6uHkZOfs3K72IxYWrfz8BlFXkRzCoIGkkLXQELIWqN/48V5xcS/z8orEd/v1666jgzMBShP2WiRlZmamo6NDdRXwgb1797Zt25bqKoC+nJzsOnSwE9+2sjIZPRq7LFKG1iIpZC00hKwFvmjixG/09LQJIX37dtPTw7dDKcOAmKTS09NxkBjdBAYGLl26tE2bNlQXAo1WziMiUUtsyMayrXN7p8TENK/Bg8pKWmKLIhFRV5hRN7QWSSFroSFkLbLoxj+MxFiBgZny+/QWutZOO43Z7VzIxZ2EkJboZlp6zNzMaisHZVdPkaF5C2yQSmgtksIuCw1hXots4Vcxti/m9x1rYu/OUdVgUl1OMxJUi4rzq6/8ndXTR8nMlk91Oc0IWYukkLXQELIW2bIrTDBmkY1pGzX57iuEECaLoWvE9ppqefsMSXtFdTXNCa1FUv/X3n0HRF3/fwB/3+Q49hAQUAQZKooDxZEzDEempjasNLfmLkdONGf+TBsO1DRNyzRHaYo7V840FQeiyHKwN8fBzd8f1/cCXODn7t7vu3s+/oIPB/ck4173eT8/A+e1MAjntZiRCwd5bd/yFEus67UoapDP1ROW/Ctj0YArdC0MQtdiRlLjNW17iWinMDWhmF+QrSnKI46utKMYB0YLV+haGISuxYyIbPguXja0U1BQJ9g2P6vU0dUyV24teY/MNNC1MAhdixnJSFWZ5mhj1pQUqjUmOhSOAowWrtC1MGjw4MH37ll0SQrANiwacDV69Gh0LaxRq9Va63wnDMAGjBaufHx8aEeAqrZu3SoQWPhhrAAsw4IYV+vXrz937hztFFAJ5goAXRgtXD1+/LiwsJB2CqgEXQsAXVgQ4wpdC4PQtQDQhdHCFboWBqFrAaALC2JcoWthEOYKAF0YLVyha2EQuhYAurAgxhW6FgahawGgC6OFK3QtDELXAkAXFsS4QtfCIMwVC7Zn746uUa11H/ft13Xrto20kiQlJXaJbHnz5nVCyPwvPp86bSytJAzCaOEKXQuD0LUA0IUFMa7QtTAIXQsAXRgtXKFrYRC6FmuzZ88v23dsmTN78ZfL5uXl5datW2/KZ3MepqWsXvuVWq1uHfHa5EkznJycX/xDCosKY2K+PnL0gJOTc8vw1qNHTapVy4MQcuHC2T9PHrkR909JSXHDBo0HfTSiWbNwU/1m5goLYlyha2EQ5oq1EYnFxcVF27ZtXLE8Zt9vfyqVygULZ5w9d3LT9zu3btl77fqVXbt/fvFPUCqVM2dNKiwqWLli3YTx0zIy02fMmqhSqUpLSxctma1Sqb6Yv3zzpl0+PnVmz/20oAD30XgJjBau0LUwCF2LteHz+Uqlcuwnn/n61pVKpa0jXsvOzpr62RwPD09391phTZo/SLr/4p9w7vzp+Phbn4ye3LxZy8jXu40bO8XfPzA/P08qlW78fsfkSTMaNgj19PQaNXJiaWnprVs3TPWbmSssiHGFroVBGo2GdgSgoH79IN0HUqnUxcXV2dlF96mtVJr/5NGLvzc5OdHe3r5u3Xq6Txs2CJ0za5Hu41KZbOPG1Tfi/snNzdFtKSjEXstLYLRwha6FQT/++CPWxKxQxbtW1/QO1iWyEonE9untGRnpkz4d0apl27mzlzRq1ESj0XTv+Zohwlo4LIhxFRMT89dff9FOAZVgrkBN2UntSktlT+/v/nnyiFKp/Hz6/KZNW4hEopKSYkoBzQxGC1fp6elFRUW0U0AlH374YUJCAu0UYE5CghuVlpYm3IvXfZqWljL5s1FJSYmFhQUODo4SiUS3/fSZE1Rjmg2MFq7Gjh3bvn172ikAgJPWrV/z8amzYcN3Z/86+feVi998+2Vubk7duvUC6wfn5uYcjP1dpVJdvHTu5s1rjo5OWVkZtPOyDl0LV15eXrQjQFU///ySI00BqhAKhV/939qly6Kj500jhLRt22HxwpVCobBr1x6pacmbt6z7asWiiIh2n0+b98uOH7f9tKm4uOitXv1pp2YXDyctcxQTE9OkSRPsuFiMo0ffbd16rJOTH+0g1mLNVPVHswP51reA8ueOx2Hty/1Da3a4AVPu3PlVq3UODR399Jes79/T0NC1MAhdCwBdWBDjauzYsVKplHYKAHiJ27fjZsyc+Lyv/rL9AE5QMyCMFq7QtTAIXQs8LTQ0bMOG7c/7KuaKYWG0cIWuBcBc1Pbyph3BWqBr4QpdC4PQtQDQhb0WrtC1AABUgdHCFboWBqFrAaALC2Jc4RpiAABVYLRwha6FQehaAOjCghhX6FoAAKrAaOEKXQuD0LUA0IUFMa7QtQAAVIHRwhW6FgahawGgCwtiXKFrYZBAIKjp/WuBltr1RNb5b2XnJBBY7gsw9lq48vLycnR0pJ0CKtm6dWtwcDDtFPBy167F5+cV56WX0w5CwcOEUhcPix2qGC1crVmz5uzZs7RTQCVqtZp2BHi5u3eT1q79xa8hKchR0M5iauVyjasX38GFdg6jwWjhKjMzs7i4mHYKqGTw4MHoWpiVkJA8ZcoyQoi3t8f33y/o+o7dlaNZJflK2rlM6ti2tIgoDe0URmS5S32mMm7cODs7O9opoBJ0LWzKyyt0dXXatm3/wIFvEkIcHf+9jv2wefzv56S+1tfD1dPWwVVEO6YRlcnURXnK8/vTuw8mtXwt+X9R3MAYoBLcwNgYsrPz5s1b/dFHb7Vr1/x5jzn/hyYxjji6CDLTXn09U6VSCYWU3zGr1WqBQPD0dqdagpICtV8DXss3eC4eNJIZ2gtuYIy9Fq7WrFkTFhbWoUMH2kHgP8/72wbTS0hIDgnxv3UrcciQtyMimrzgke3e4rd7i6gUWq32VRbqjx0798MPv6WnZ8+ePfqNN9pxiMzVwYNnVSpNnz5dqmzXarViibV0EBgtXKFrYdDgwYOjo6NDQkJoB7F2o0fP9/PznjVrVJcuEdX8FqG4xs/y6FHGd9/99Pfft4qLZQ4OUicXicimxj/EgPr265SYmEY3A3UYLVyha2EQuha6rl697erq5O/vO2rUO+HhoUZ9rm3b9u/deyw19QmfzyeESCQ2Li70TwYIDKxLCOnbd/yvv34tFltye/Q81rJ3Zjyenp64qzZrcF4LRb/8Erthwy4PD1dCiLHnyrBhszZu3P3wYYZurhBCRCKRvb2tUZ+0+n7+eXlMzA7aKejAaOEK57UwCOe1mN7Vq3d++GEvIaRDhxbr18+3szPFJSpSUp7IZPKKW9RqtYuLkwmeujrs7GwnTRpECLl48QbtLKaG0cIVuhYG4bwWU1KrNdnZeRs27IyMbEMI8fU13bXA//xzi5+fN5//7+KnRqMRCgUODsytIhw4cOr69bu0U5gURgtX48aN69ixI+0UUAm6FtNITEwbPXp+ebnC0dF+/fov/Py8TZ9hz55vmzQJdnKy151H4eTkYPoML7Vo0aTs7DzaKUwKo4UrdC0MQtdibOnp2YSQ2Ngzo0a9I5VKbGxqflyXgeTmFkRHjz1xYvPVq7uFQoGTE6N/jLrjoRcvXk87iIlgtHCFroVB6FqMp7CweNSoedeuxRNCJk78yNhF/UtJpRL93tLly7+uWjWHbp4Xe+215tu3H6CdwhQwWrhC18IgdC3GoGsL0tLSx4x5r2dPJhaBFyxYe+zYBdopaqBz54hOnSJ0BRXtLMaF81q4wnktDELXYnAzZqwkhDRr1qBJE1ZWGlNTn/j4ePbuXfWkd8b5+HgQQt58c/TOnSvZbIYMAtcQA6gE1xCr6PLlOEJ4ERFN7tx50KhRfdpxLMqGDb+OGvUu7RScvOAaYlgQ4wpdC4PQtRjEkSN//fjjvuBgP0IIa3PlzJkrR4+eo52CE91cMfff4nkwWrhC18IgdC1cXL588//+b5PuXPo1a+Y6O9O/bkoV+flFCxfGREW9RjuIAdy4kXD+/HXaKQwPo4WriRMn4rwW1ojFYv2VP6D6SkvLlErVli2/vf12V0KIuzujN0GUSiWHD39PO4VhTJs2TKOxwEoff35cubu747wW1mzevDkoKIh2CnOSlPRoxIi5eXkFQqFg7drooCB2q6b8/KLHj7MEAst57WrfvgUhZPr0r2gHMSTL+eehZdWqVWfOnKGdAipRKpU4PqWakpIeEUIuXrw+YcKHvr5e7B9Z17fveE9PN9opDG/gwDc3btxNO4XBYLRwlZ2dXVJSQjsFVDJkyJB79+7RTsE6mUw+dOhs3ZUTP/igV9OmDWgnermrV2+vXz/fzo6VaxsbUPPmDfv3j9Ld5pl2FgPAaOEKXQuD0LW82IUL15VKVUFB0WefffzBB2/SjlMD4eGhDRoE0E5hLLo7zXzyyRcZGTm0s3CFPz+u0LUwCF3LCyxbtnH79oNCocDHx5Od8x+rY8aMlQ8epNFOYXQ7d648duw87RRcYbRwha6FQehannbhwvVDh84SQt55p/uqVbPZ71SqOHr0XL16PvXr16UdxBQGDepNCNm16wjtIK8Oo4UrdC0MQtdSxdWrt7dvPxgR0YQQEhDgSzvOq4iKem3MmPdopzCp/PzCP/+8SDvFK8Jo4QpdC4PQtehcvXp72rTlulu1r1o1283NmXaiV3Tx4o2UlMe0U5jaqFHvOjkxd75qNeHPjyt0LQxC15KfX0QI2bv3+IgRA5i9QVY1Xb16e/Pm3+rV86EdhILw8EaEkNGj59MOUmMYLVxt2bLl3DnLvAqQ+bLyruX3309cvXqbELJ48aSQEH/acbiysRF/990s2ilomjZtWEzMDtopagajhaukpKTCQks4Dt2SWHnX0rNnx7S0dItZQWrcOIjiXSxZEBhYd9iwfrRT1AxGC1foWhhk5V2LWCwaNqyfZawgDRkyMzn5Ee0UlKnV6suXb9JOUTPW++dnKOhaGISuRef996fIZHLaKV7d1au3GzSo7+9vloe0GZBCoZw16xvaKWoGo4UrnNfCICvvWvS2b1++Zs122ileXXh46IwZI2inoE8g4Hfs2JJ2iprBaOEK57UwyMq7Fj0+nz99+nDaKV5RXl7hlSu3aKdgglgsXrx4Eu0UNYPRwhW6FgZZeddSRWJi2ieffEE7RY3NnfudRd7I5BWo1eqzZ6/STlEz+PPjCl0Lg9C1VBQYWHfGjJG//XacdpAayM0t6NWrc0REGO0gTEDXYo3QtTCorKwMb3gr8vPz1t040ly4uTn36NGBdgpWoGuxRuhaGDR8+PD79+/TTsGcn38+YBZn3mVm5ixcGEM7BUPQtVijyZMnd+7cmXYKqEQikQgEAtopmPPhh72aN28YF8f6AQ4xMTubN29IOwVDzLFr4eEYTYCKjh59t3XrsU5O7N4cniONRsPj8Zi9qL5Wqy0tldvZSWkHYYhcXhYVNfLs2W20g1R1586vWq1zaOjop7+EvRauvv3221OnTtFOAZWga3kBPp8/ceIS3X2LGVRUhOXlqtC1WKPc3NzS0lLaKaASdC0vtmrV7Ly8wtzcAtpBqpLLy3r1GotdlirQtVgjdC0MQtfyUj17dhSJhKzt25069ff8+eNop2COOXYtGC1cubq6SqV4k8WWTZs2BQYG0k7BOkdH+3btPlSpVLSD/KdHjw6RkW1op2AOzmuxRuhaGISupZrOnNl66NBftFP8659/7ly6FEc7BYsEAn6XLhG0U9QMRgtX6FoYhK6lmsRiUc+eHbKy8ipuHDqUzn23Jk9e2qRJMJWnZpxYLF6wYALtFDWD0cIVuhYGoWupPoFA8PBhxujR8wghb789ITx8QF5e0cOH6SaO8eRJ1ubNS6RSiYmf1yyo1epTpy7TTlEzGC1coWthELqWGgkPbzR//viePUc9fJjB4/GysnJNf1qlt7dH/fp1TPyk5kKhUM6du4p2iprBaOEKXQuD0LXU1MiR0VlZ+bqPlUrV+fPXTPnsR4+eX7lyiymf0byga7FG6FoYhK6lRqKihmdk5FTcEh+fbMoAO3fGvvdeT1M+o3lB12KN0LUwCF1L9Y0bt9DGxqbKxpIS2bVr8SbLsGnTIh8fD5M9ndlB12KN0LUwCF1L9a1ZM/fLLz8dOLBnQICvvb2dWq3W3S7lwgUTrYlduxYvk2G//0XMsWsR0g5g9r799tumTZtix4UpZWVluNFk9YWGBoWGBimVypMnL8fGnk5JefLoUeY//5hir+XixRs//fTH6tVzTPBc5sscuxaMFq7QtTBo+PDh0dHRISEhtINUy/VTvPs3tHw+LyOV7onxfELaBAnbBAUSbX2tVkvWTFUb+ym12tCGksYmeCJDqV1PqFRo64XyWncz3TXjzbFrwWjhavLkyRIJDsZnixl1LfvWE08/pxaRtu61bQif0QvdQ0X5GeX5WeWb52cPjeabplLQXUOsc2dz2nHBaOHK1dWVdgSoatOmTbQjVMve1dp6jd2DmjvSDgI14OZt4+Zt41LLZvOCR0Pnm2K26LoWBu/X8gJYjObq66+/xnktrCktLdXV0Sy7c1HrWc8Jc8VMufnYNOtS6/IRUzyXOXYtGC1c5efno2thzciRIxMTE2mneInUBOLoWvWoXzAjLh42ybdN0biYY9eC0cLVZ5991qVLF9opoBKpVMp+16JV81290dKZMTcvG6HIFC+hOK/FGjk7O9va2tJOAZV8//337J/XkpuhIVrTHWUEBqclJCPFFAf1meN5LRgtXKFrYZBZdC0A1YSuxRqha2GQWXQtANWErsUaoWthkFl0LQDVpFarT5y4SDtFzWC0cIWuhUFm0bUAVJNCoZw/fw3tFDWD0cIVuhYGoWsBSyIQ8Lt2bUs7Rc1gtHCFroVB6FrAkojF4nnzxtJOUTMYLVyha2EQuhawJOharBG6FgahawFLgq7FGqFrYRC6FrAk6FqsEboWBqFrAUuCrsUaoWthELoWsCToWqwRuhYGWWTXcu/+3S6RLW/fjqMdxAAWL5kzYdJw4/38wUP6r1rzFSEkKSmxS2TL+mGk/QAAIABJREFUmzevG++5TABdizVC18Igi+xa3FzdBw8a4e7u8eKH7f1t59Jl80wVqqr5X3wee2gfrWe3VOharBG6FgZZZNfi5uY+dMgYT0+vFz/sbsJtUyVi7tktFboWa4SuhUGOjo6W17VUXBCbGz11wcKZh4/80btPlze6tZn82aj4u7cJIRMmDT92LPbo0YNdIlveu3+XEHLz5vWp08a+1bvzx0MHxKz7RiaT6X7a7j3bB7zb/a9zpyLfiFi15qv7iQldIltevPjXgHe7jxg1kBAS1b3tjp1b9c++dNm8seOH6D7u8Wb7X3b8OCd6SpfIlr16d5o159PikmKVStUlsmVmZsbyrxa+1afzS38dkVB07fqVAe92f6Nbm0/GfXwn/pZue3Lyg2+/WzZ4SP/uPV8bPeajAwd/039L7z5dduzcuumHtbrnXbBwZl5eru5LKSlJYz4Z1LNXh1lzPtX9p3im2EP7Phn3cY8324+bMHT3nu3a/93UYG701IWLZq3f8F2XyJZJScy9KUHXYo3QtTAoJibG8rqWisRi8ZUrFy9cOLtu3U+HDv4lFomX/d98Qsiqbzc1bNg4KurNkyeuBAc1SEtLmT5jvFKlXLN6y7y5X96/f3fK1DEajYYQIhKJ5fLSHTu3zpyx4O0+74pFYkLIxh/WvPfuoCmfzXnxs4tE4t17tvd7+/0Txy4vW7oqLTV59ZqvhELh4dhzhJBpU+f+se/lS8RZWRl//LFn9qxFXy79TqEoX/7VAt32VauXX7l66bPJs3ZsP9CzZ98VKxf/feXfV1Wxjc327ZttbCT7953c8sPuuJvXtm77nhCiVCo/nzmhVi3PzZt2jRg2bvv2zQX5eU8/47Fjscu/WtggpNH2n/YPHTJm1+6f16xd+b/fSJSQcCcpOXHxwpW1a/vU/B/EuNC1WKMVK1acPHmSdgqopLi42PK6lor4fD4h5PPp871r+wiFws6d30hNTX56Yfb4iUMioWjB/OV169YLCAicNi064V78+QtnCCECgaC0tHT4sLFdI7v7+tbV7eS91q7TOwM+bNgg9MXPzuPx6gcEtWjeis/nh4aG9e494NSpYypVzW6KlZWd+emns5o3axneIqLf2++npCQVFhYQQubNW7Z82ZpmzcKdnV369B4QFBhy+fJ5/fOGhDT66MNhDvYO7u61wsNbx8ffIoScOftnVlbmuLFTPD29AgICx4+bWlxS/PQz/nFwb1hY80kTP3dxcW0Z3nrYkE9+3/er7kkFAkFObvaC+cvbtevI4DtFdC3WqLCwUC6X004BlYwZM8byupYq6tStJ5VKdR/b2zsQQoqLi6o85tatGw0ahDo5Oes+re3l7e3te+PGP/oHhAQ3qvj44KCG1Xz2+vWD9R/7eNdRKBSPHz+sUf769YMd7B10Hzs4OBJCysrKCCFajWbXnp8HfdyvS2TLLpEt7ycmFBT8twsSHPxfQnt7B5mshBDy+PFDiUTi5VVbt93T08vNzb3K06lUqjt3brZq+d8LdPPmrdRqtf7gMb+6/jY2NjX6FUzGHLsWIe0AZm/q1KlisZh2CqjEw8NDKLTw/7d1Oy4vVlJSrCtRKm7Mz8/Vf1zlf11xtV9bbWwk+o8ltraEkFJ5zQ5meeY/kFqt/nzGBK1WO2rkhGbNWjrYO+gLHh0ej/f0dxUVFdrZ2VfcIpFU3fMoKytTq9Wbfli76Ye1Fbfn/29uVf93Nz21Wn3q1N+RkW1oB6kBC//zMwFHR0faEaCqr7/+mnYEJri6uTextR06ZEzFjU6Ozq/wozSVFxh1uws6ZXI5IURqK+WQ9F8JCXfu3b+74quYFs1b6baUPGtpqwpHRydFeXnFLaWlsiqPsbe3l0gk3bu91bFjZMXtPt51uMc2Nl3XgtFiXVasWNGiRQscJMaU4uJinJBPCKkfEHTy5NFmTcP1b/ZTUpJ8fetW53ttbGzkFXZE0tJSBBX2M27cuKr/+H5igkQi8fb21R0gwIWu+XB3q6X7NCkp8eHD1JDglyzTeXnWLi4pTk1N9vPzJ4TcTbiT/6waPyAgSF4mb97s3304hUKRmZnu4eHJMbMJoGuxRuhaGGQNXcvz+PjUSUi4c+36lfz8vHffHaRSq1avXVFWVpaWlrJu/bfDRryXnPKgOj8nNLTp2b9O6g5W3vbTpty8nIpfzc7J2r1nu1qtTk1N/uPAno4dI0UikY2NTa1aHv/8c/na9Ss1bfV16vnX5/F4u3b/XFJSkpqavDZmZauWbTIy01/8Xe3adRKLxV+tXFRWVpaTk71k6VxdeVPF6JETz5w5EXton0ajiYu7tmDRzCnTPimvvLvDJnPsWjBauJo6derrr79OOwVUYpHntVTTW2/202q1U6eNfZB038nRadPGnRIbyehPPvp46IAbcf98Pm1eUGBIdX7OhPHTnJ1cevXu9Ea3NuXlZV0je6grTIu3evWLi7vWNar1kGHv1A8IGj9uqm77hx8Mu3L10tzoKfKyV3m/VdvLe/asRTdvXX+rT+c50VOGDx/Xu/eAW7duDBvx3gu+y97efvGir8vk8l69Ow0ZNuCdAR/WqeOneeoQwbCw5utjfoqLu/Z2/zemfT6uVCZbtHAls9V9RWq1+ujRc7RT1AxPf9IQABBCjh59t3XrsU5OfsZ+om1LtK8PrOPoKjL2Exlcn7cj+/cbOHjQCNpBKNNqyLZFieNWGP1NjFxeFhU18uzZbcZ+opq6c+dXrdY5NHT001/CXgtXOK+FQRZ/XgtYFYGA363ba7RT1AxqfK7QtTBozJgx0dHRISHVWvkBY+jbr6v6OXXLrJkL27btYPJEZkwsFs+ZM6YaD2QIRgtXOK+FQdbctZjAvt9OvPQxMWu3Pu9LLs6uhk5k4XTXEIuKMqcdF4wWrnBeC4NiYmJoR7B2tb28aUewHAqFcuHCdeY1WtC1cIWuhUHoWsCSmGPXgtHCFboWBlnzeS1gecyxa8Fo4QrntTAIXQtYEnM8rwWjhStHR0eJRFKNB4LpWPz9WsCq6LoW2ilqBqOFK3QtDELXApYEXYs1QtfCIHQtYEnQtVgjdC0McnZ2tvj7tYD1QNdijdC1MGjNmjX169ennQLAMNC1WKPly5efOPHyk5PBlAoKCl7tou4ADELXYo2Ki4vN4pYPVmXcuHEPHlTrriQUuXjw+c+6HS+YCx6PeNYVEeNfOx5dizWaPn16ZGRkNR4IpmMmXYs2PwtvSsxYQbZCUaYmxn97gK7FGtnb25vF3YSsill0Lb6BWlkBVu3MWFGesm4DU+x3omuxRuhaGGQWXUuzzrz4v3MLshS0g8ArOrkzvX0fUzyRUCjo2bOjKZ7JcDBauELXwiCz6FoIIR9O55345cmjhFJluYZ2FqgutUqb9bD8pyUPRiwy0cWERCLRzJkjTfNchsL+ejTrpk+fLhKZ3z1oLZuZdC1EICJDorWn92Se+U1d219SlMP6nlaNaLVaLSEWdqiCs5fw4d2y4BaCEQv4IlMthKtUqmPHLvToYU73TzODPz/G2dvb044AVa1Zs4Z2hBro1J906i8oylWoLWqykIMHT2dn5Q4d2o92EIPiKVyGmfrKp0qlasmSDRgt1mX58uUtWrTAQWJMKSgosLe3N4sdFz1HN4t6d08ICWjg5OxBXDwt7fcyPXQt1ghdC4PMpWuxbO3aNe/VqzPtFJbAHLsWjBaucF4Lg8yla7FsqalP7t5Nop3CEqhUqkOHztJOUTMYLVzhvBYGmcV5LRbv/PlrBw+epp3CEui6FtopagajhSuc18IgszivxeL5+XmHhPjTTmEJ0LVYI3QtDELXwgJ0LYaCrsUaoWthELoWFqBrMRR0LdYIXQuD0LWwAF2LoaBrsUboWhiUl5eHroU6dC2Ggq7FGqFrYdCECRPQtVCHrsVQ0LVYoxkzZnTt2pV2CqjE1dUVF3ajLiXlcXw8BrwBoGuxRlKpVCwW004BlaxatSogIIB2Cmt34cL12NgztFNYAnQt1mjZsmXHjx+nnQIqQdfCgnr1fBo2xMEUBoCuxRrJZDKFAndzYgu6Fha0bdvM7F4Q2YSuxRqha2EQuhYWoGsxFHQt1ghdC4PQtbAAXYuhoGuxRuhaGISuhQXoWgxFKBS89ZaZHcaN0cIVuhYGoWthAboWQxGJRNOnD6edomYwWrhC18IgdC0sQNdiKCqV6sCBU7RT1AxGC1foWhiEroUF6FoMRalULVu2iXaKmsFo4QpdC4PQtbAAXYuhoGuxRuhaGISuhQXoWgwFXYs1QtfCIHQtLEDXYijoWqwRuhYGoWthQXp69oEDuF+LAWg0mt9+M7NVd4wWrtC1MAhdCwvatm0WGFiXdgqzV16uuH49YfXqObSD1AxGC1foWhiEroURb7/dlRCydet+2kHM1ZUrt5KSHkVENLG1ldDOUjMYLVyha2EQuhamNGoUsGrVT7RTmJ/c3IKNG/c0bGiWS7tC2gHMnlQqpR0Bqlq1ahXtCPCfli0bm92bbuoyMnJKS8vWrZtHO8grwl4LV+haGJSTk6NUKmmngP+EhgYSQqZM+T/aQczDwoUxPB4JCPClHeTVYbRwha6FQZMmTUpKSqKdAqqaPXs0pstLJSQkN20a4unpTjsIJ1gQ42rWrFlCIf4zsqVWrVroWhjk6uq0fPlUQkhmZq6npxvtOCyKj0/y9vYICfGnHYQr7LVwJZFIMFpY88033+C8Fjbx+XxCyJIl6x89yqCdhS0qlapt24EBAb5OTva0sxgARgtXS5cuPXbsGO0UUAm6FsZ9++2s2Fgzu22iUclk8ps37585s83GxkLOv8Zo4Uoul+NVjDXoWtg3atQ7hJCjR8/TDkLf8eMXnjzJat68oUhkOesfGC1czZo1KyoqinYKqARdi7m4dy/lwoXrtFPQlJGRc/z4haAgP9pBDAyjhSt0LQxC12Iuxo//QKGw3p3+x48zFQrll19+RjuI4WG0cIWuhUHoWsxIp06tCCFLlmygHcTUpkxZZmMjrlu3Nu0gRoHRwhW6FgahazE7ERFN9u8/STuF6dy+ndinT6S7uwvtIMaClRyucF4Lg9C1mJ2uXdumpj6hncIUZDJ5UtLDgIA6uisUWCrstXCFroVB6FrMkZ+fNyGkV69PaAcxIrm8rGfP0Y0aBdrZ2dLOYlwYLVyha2EQuhbztWXLki1bfqedwjAGDpxa8dOiopLExLTTp7cKBJb/wmv5v6GxoWthELoW8+Xu7jJoUG+5vCwtLV235fXXh/buPZZ2rhrbunVfQkKy/tPdu4/m5hY0aRJMNZTpYLRwhfNaGISuxawJBHxbW8nkyUuzs/OiokYUFZVkZubt3n2Edq6a+eOPUzwer0WL/oSQR48yEhPT/P3N+ErGNYXRwhW6Fgaha7EAe/d+98EH0/LyCnXX1zpw4DTtRDVw7ty17Ow8Ho/H5/NbtOjP5/NnzBhBO5RJYbRwha6FQehaLED//pPy84t0H/N4vPT07Fu37tEOVV2//36suFim+5jP5/ftO552IlPDaOEKXQuD0LWYu379JlY5FjknJ//gwTP0EtXA48eZt2494PF4+i0ajbZ9+4+ohjI1jBau0LUwCF2LubOzs/Xx8bCxEWs0Gt0WHo93/vw1s3gbd/Dg6ZycfN3HGo1Gq9W6uDjWquUyceJi2tFMByUBVxIJbvrNnG+++YZ2BOBk27ZlDx+mx8XdO3/+WmJimlxelpGRk5mZd/Lkpaio9rTTvcThw3+pVCoXFydXV0exWBQeHhoWFvLGG+1o5zIpnlarpZ3BvC1evLhVq1bYcWFKRkaGm5vbq+24HD36buvWY52cLO1KtAwqLSZ/H+NnpmrKSonm+XsjWqLVaDRqtUalUkttzeCdnKxULhDwBQIBn88X8J+7MuRSm28jIcEttAFNzPVF+M6dX7Va59DQ0U9/CXstXJWXl6tUKtopoJIpU6ZER0eHhITQDgLPlZnG/+N7dXhX1zohIjtHoRW+xdWotTmPy+7fKM1MK2v7poZ2HAPDaOFqzpw5/Oe/MQEqvLy8xGILuVufRXqUSC4c4L831dp3DR1cRf5NHP4+knP2t5IOb1vUdMVrIldisRjntbBmxYoV/v7+tFPAs2k15GIs/41BVnT+4Iu16uZeJrdNu0s7h0FhtHC1ePHio0eP0k4BlWRkZJjFoUTWKSNFq9XwBUJeNR5rLRxdbVLvYq8FKkDXwqApU6bgvBZm5WWS2vWltFOwxc1HUlZqUa/GWMnhCl0Lg9C1sExRplWW0Q7BGB4hRTkWtdeC0cIVXsIYtGLFCtoRAKwa3m5zha6FQehaAOjCaOEKXQuD0LUA0IUFMa7QtTAIXQsAXRgtXOEljEHoWgDowtttrtC1MAhdCwBdGC1coWthELoWALqwIMYVuhYGoWsBoAujhSu8hDEIXQsAXXi7zRW6FgahawGgC6OFK3QtDELXAkAXFsS4QtfCIG9vbxsbG9opAKwXXhO5wv1aGLR8+fJ69erRTgFmLzc3p0tkyzNn/6QdxPxgtHC1cOHCw4cP004BlTx58kShUNBOAWC9MFq4UiqVGo2l3dfa3E2bNi05OZl2CgDrhZUcrqKjo9G1sAZdCxBCcnKy18asvH0nTi6Xt2792uCPRtSp40cISUy8N3L0B/+3bPW+/bvOnTvt4eHZpXPU6FETeTweIeTEn0c2b44pkZW0bdNhQP8PaP8S5gqviVwJhUKMFtagawGVSvXZ1DE3b12fOmXulh92OTo6jRs/5En6Y/25aCtWLuoa2ePo4QszPv9i56/bTp46RghJSkpcvGROVFSvrT/u7dq1x6o1y2n/HuYKr4lcoWthELoWuBH3z8OHqTNnLGjVso2rq9v4sVMcHJ327t1BCNG9F3yz59udO3UViUTNm7X09PS6e/c2IWTf/l2eHl6DB41wdHAMbxHxZo++tH8Pc4XRwpVEIsFeC2uWLVuGrsXK3bx5XSQStWjeSvcpj8dr1jT85s1r+gcEBzfUf2xv71BSUkwIefz4YT3/+vrtDRqEmja15UDXwtWnn36K0cIasViMrsXKlZQUK5XKLpEtK250c3PXf/zMP9uiosK6df9bSpVIbI0c02JhtHCFk1oYtHw5lsitnZubu62t7eJFX1fcKBS85K/V0dGpvLxc/2lpqcxoAS0cXha5WrhwYatWrbp37047CPznyZMn7u7uuHKoNQsICJLL5V5e3rW9vHVbHj955Ori9uLv8vSsffHSXxqNRrdPc/HSXyYJa4GwksMVzmthEM5rgdYR7SIi2i1fviAzM6OwsGDvbzs/GTv40OH9L/6uzp3fyMvLXRvztVarvXb9yv79u02V19Jgr4UrnNfCIJzXAoSQpYu/2f/HngWLZt65c7NOHb/u3d7q9/Z7L/6WVi3bjB418Y8/9uzZ+4unp9esGQsnfToS7x1fAU+r1dLOAMCQo0ffbd16rJOTH+0gFuvaSU1BjnPLqJesTVmVrLSy6yfT+0+knaOG7tz5Vat1Dg0d/fSX8HabK5zXwiCc1wJAFxbEuELXwqBp06ZFR0eHhITQDgIGMODd7uVlZU9vV6lVAoGQ95zv+mX7AXt7e0NlmBs99fr1K8/8krOLa0F+3tPbeXz+/t+t95LJGC1coWthELoWS7Llh91aUuN1ewPOFULIzBkL1Br1M7+kUiqFIpEBn8syYLRwhfNaGITzWiyJYYfEq5FKpbQjmBm83eYKXQuD0LUA0IXRwhW6FgbhvBYAurCYw9X8+fN1t3kAdtSpUwddCwBFGC1cocNn0Jdffkk7AoBVw8siV1988QW6FtY8evSo4kUGAcDEMFq4UqvV6FpY8/nnn6ekpNBOAWC9sCDGFboWBqFrAaALo4UrdC0MQtcCQBdeFrlC18IgdC0s4wuJCLuUlfH5PDtHi1r8wGjhCl0Lg9C1sMzBhZeX8YxrglmzghyFQGxRF6HHghhX6FoYhK6FZe61eVrts6/HZbXkJUovPy0hlvNKgr0Wrvh8PkYLa7788st69erRTgHP5uhGavmor57IoR2EFUW5ygfXC5q8ZlEvIxgtXKFrYRC6Fsa160WEgtK/j+SqVRa1CvQKniSWnvjl0ftTLWquYEHMANC1MOjzzz/H/VoY176P+uqJgv3rCnk8nqOrWK2mMGO0Wq1Wq6V1kKfEjpdyWx7Sgj94tqXNFYwWA0DXwiB0LWYhPJLfvAspzCGygnIqOy9Xrty6cSNh+PD+NJ6ciMS87oMEfAGVJzc6jBaucF4Lg3Bei7ng84mLB3HxoPPmLD6lSCF4VCcYbw0NDy+LXKFrYRC6FgC6MFq4QtfCIJzXAkAXFsS4QtfCIHQtAHRhtHCFroVB6FoA6MLLIlfoWhiUmppaVoZLiQBQg9HCFboWBs2aNSs1NZV2CgDrhQUxrhYsWEA7AlTl5+eHrgWAIowWsEBLliyhHQHAqmFBjKvo6OjY2FjaKaASdC0AdGG0gAVC1wJAFxbEuELXwiB0LQB0YbSABULXAkAXFsS4QtfCIHQtAHRhtIAFQtcCQBcWxLhC18IgdC0AdGG0gAVC1wJAFxbEuELXwiB0LQB0YbSABULXAkAXFsS4QtfCIHQtAHRhtIAFQtcCQBcWxLhC18IgdC0AdGG0gAVC1wJAFxbEuELXwqCgoCAOXQvv77/XCYW2ho0EDEpIKCwpKTl1Cn/Cr0gmy6xXr88zv8TTarUmz2NRNBoNj8fj8Xi0g4Bh5OfHq1SltFOAKZw+/feFC9dnzBhJO4gZs7PzlkprP70dey1czZ8/v02bNj179qQdBP6TlJTk7e0tkUhe4XtdXBoaIRGwyNEx18YmrVatcNpBLBC6Fq4EAgGfj/+MbJk7dy66FgCKsNfC1bx582hHgKoCAgJebZcFAAwCo4UrdC0MWrhwIe0IAFYNKzlczZ8//9ChQ7RTQCVJSUk4rwWAIowWrtC1MAhdCwBdWBDjCl0Lg9C1ANCF0cIVuhYGoWsBoAsrOVyha2EQuhYAujBauELXwiB0LQB0YUGMK3QtDELXAkAXRgtX6FoYhK4FgC6s5HCFroVB6FoA6MJo4QpdC4PQtQDQhQUxrtC1MAhdCwBdGC1coWthELoWALqwksMVuhYGoWsBoAujhSt0LQxC1wJAFxbEuELXwqCgoCBbW9zcHoAajBauVCoVn8/HjgtT5s+fTzsCgFXDCyJXCxYsOHz4MO0UUEliYiK6FgCKMFq4EolE2GVhzbx589C1AFCEBTGu5s6dSzsCVIWuBYAunlarpZ3B7K1du7Zly5YRERG0gwBAtSQmJsbFxZ08eTIwMHDSpEm041gg7LUYgK+v75YtWyIiIkpKSq5cudK5c2faiazd/fv369ata2NjQzsIsEIul8fFxd24cSMuLi4uLs7b27tp06bdu3fv1q0b7WiWCXsthlReXj579uz09PSff/45OztbKpXa2dnRDmWN+vbtu3r1al9fX9pBgKZHjx7px8mjR4/CwsKaNm0aFhYWFhYmlUppp7Nw2GsxJBsbm6+++kr3cWFh4YABA4YPHz548GCZTIYZY0r169fHa4cV0mq1FXdNpFKpbpYMGDAgKCiIdjrrgr0W40pOTvb399+9e/e+fftmzJgRGhpKOxGARcnJydGPk1u3bun3S8LCwlxdXWmns14YLSYSHx+vUCiaNm26cuVKgUAwfPhwe3t72qEsVnx8fHBwsEAgoB0EjCIhIUG/a6L7s9LNkiZNmtCOBv/CaDG13Nzc2NjYiIiIkJCQDRs2NG7cuF27drRDWZr27dsfP34c19W3GDKZTD9Lbty44e/vr9818fb2pp0OngFdi6m5ubkNGjRI97G/v/+OHTvCwsIkEsmff/75+uuvC4X4FzGAkJAQHB5m7lJTU+P+JzMzUzdIhgwZ0rRpU/zjsg97LUzQaDRz5869e/funj178vLyeDyei4sL7VAAJqVWq/W7JnFxcU5OTvpdk/r169NOBzWD0cKcx48fDxkypG/fvuPGjcOhZa9Ao9HEx8fjiAmzkJmZqZ8lCQkJ+lnStGlTJycn2ung1WG0MEp3aNmRI0c2b948ZcqUVq1a0U5kNoqLi3v37n3y5EnaQeDZ7ty5ox8nWq1WP07wbsCSYGWfUf7+/oSQbt26BQYGFhUV6S4nI5fLhw8f7uzsTDsd65o3b047AvynsLBQP0tu3LgRHBwcFhbWuXPnSZMmeXp60k4HRoG9FrNRWFgYGxvbqFGjpk2bbty4MSgoqFOnTrRDATxbUlKSfpzk5+dXXOnCsSrWAKPFLJ07d27v3r1Tp06tXbv24cOHO3fujANt9crLy5OTkxs0aEA7iHUpLy+vuGvi4eGhnyV+fn6004GpYbSYvYULF164cCE2Nra4uFgul3t4eNBORNmDBw9mz569Y8cO2kEs35MnT/TjJDk5WT9LwsLCcEawlcOeqdnT3zBGoVAMGTKkQ4cOM2fOtOZDy4RCIQph47l586Z+10QsFuvGSZ8+fUJCQmhHA4Zgr8XSpKam+vn5nT9/fuXKlZMmTerQoQPtRGDe8vLyKq50NW7cWL9r4u7uTjsdMAqjxWKlpqZmZWW1atXqhx9+yMzMHDlypJW8EJSUlGRmZuIkOy7u37+vnyWlpaUVV7p4PB7tdGAGMFosn1wuj42N9fX1bd269Y8//ujr6xsZGUk7lOGNGjXq77//5vF4Wq1W9/LH5/NVKtX169dpRzMDpaWl+lkSFxfn6+urnyW47Q28AnQtls/W1rZ///66jxs3bvzrr7/WqVMnODj4yJEjbdq0sZhzngcPHvzgwYPCwkL9FrVajUt/vsDDhw/14+TJkye6XZPBgweHhYXZ2trSTgfmDXst1uvrr78+ePDg8ePHy8rK8vLyLOAKskOHDr1586b+U1dX12XLluH0ST39nbJ0uyb29vb6XZPAwEDa6cCiYLQAkclkAwcODA0LiNrwAAAJuElEQVQNXbp0qVkfWnbkyJElS5bIZDLdp5GRkcuWLaMdirKn75Slv7sJLoEKxoPRAv/SHVoWFxc3e/bsCRMmREVF0U70KgYOHHj//n1CiIeHxzfffBMcHEw7EQW4UxZQh9ECVaWnp6emprZp0+aXX365f//+8OHDfXx8nvfg7t27L126lJ1Fpz179ixfvlypVPbt21d/xo/FKykpqXhPeNwpC6jDaIHnUigUhw8fdnZ27tix4/bt211cXKKioqrcFbhFixa1atVavXp1UFAQvaT/USgUAwcOVCqVq1evrlu3Lu04RpSamqqfJVlZWRXvCY87ZQF1GC1QLbdu3dq5c2f//v2bNWt29OjR8PBwNze3vn37pqWl8fl8V1fXiRMn9urVq/o/MOeJ4kmSPC9DUVKo1miIrEBpqKj5BfkatcbNzc0gP83RTaxWaeydhK6eIk8/SW1/atdqU6lUFe+U5eLiop8lAQEBtFIBPBNGC9TYhg0b9u7du3fv3jfffLO4uFi30c3Nbfr06S89Y0ZWqLp+uvDu1WK+gO/oaU8IT2gjEEmELJ+HpyxTq8pVGg2R5ZaUlSjrh9k36+jk7mOKPYPMzEz9OLl3717FXROLOWocLBJGC7witVrdqlUrPp+v3+Ls7DxlypQePXo88/HKcu3ZfTkP4kpq+bvauUpEErM8p0qt1BTnyPMfFdTyEXfs6+boJjL4U9y+fVu/a8Lj8fSzpFGjRgZ/LgAjwWiBVxceHl7lsh+Ojo5Tp07t2bNnlUc+iCs9H5sndbVzq+No2ozGUpghK3hS1LitQ/jrL9l7WL9+/Z49e2xtbfft2/fsH1VYWHGlKyQkRD9OcKcsMFMYLfCKunXrlp2drbuwikAgcHZ2lkqlfD7fy8srJiam4iOvHC+IvyqrE+ZFL6yxZN7Pca3Ff2Ngrec9YObMmWfPni0rK7Ozszt9+rR++4MHD/SzpLCwsOKdsqocKAFgjjBa4BV16dLFzc3Nx8fH19c3MDDQ19fX29v76cOU4y/L4i7IPIMNU6ozKO9RkZubtlM/1yrbc3JyPv300zt37uh27DQazbp16/TjxNPTUz9OcKcssDwYLWBEV08UJN5SWPBc0cl7WGRnq+g26L/Fq8uXLy9YsCA9PV2/YKjRaNq0aaPfNTHfSx4AVAdGCxhLWoL8zO95vpa4Dva07KTc+o3ELbo4E0J27Nixbt26kpKSKo/x8/Pbs2cPpYAAJsWvxmMAakyl1J7bn2slc4UQUivALelWWdZDBSHk/fffb9asmY+Pj6urq0aj0Wg0usfI5XLaMQFMBHstYBSn9+Tk5QtcfCzkeLDqKMmTl+cX9h//b9tUXFyckJBw69atS5cupaWlyWSykpKSK1eu0I4JYAoYLWB4siL1z8vSgttb8nVWnunRzYxOfVzqhEirbC8vL4+Pj2/WrBmlXACmhgUxMLx//iyoFVD1iCl27Nq3dMWaj4zxk13ruFw7U/T0dhsbG8wVsCoYLWB49/4ptne1xtsUSp1tHt+XKcuxEgDWDqMFDCz7cblAxBdJrPS8PycvafLtqseGAVgbs7yOE7DsSVKZc20H4/38S1f3X7rye0bmg9peQU0bR3Zo+77u3JEt26cLBKIGQW33H/pGoZD71Q3r1W18Xd9QQkh5eenPu6MTk67U9gx8rfUA42UjhNi72WemKYJbGPVJAFiHvRYwsPxMhcZoC0JXrx/a9ftiX++GMz/7rdvro86c/2X/oW90XxIKxfcSL91J+GvyJz8uiT4tFIp27l2o+9Kvvy/OyX04esjqjwcue5x+L+H+RWPlI0Qg5GemlRnv5wOYBYwWMLCSApVQbKzVsItXfg/wa97vrWkO9q7BgRHdI0efu7RLJisghPB4fELI+/2i3Vx9BAJh08ZdM7OTy8tLC4uyb9w63qX9IL86jR0d3Hp1myASio0UjxAitBHIilTG+/kAZgGjBQxMoyEiG8Nfap4QolarUh/eDA5qrd8SGNBSo1Enp97QfepRq56Nzb8H/tpKHAghpfKivPzHhBBPD3/ddh6P5+vdwBjxdEQSIV+IPyuwduhawMAU5Rq+Sm2Un6ws02jUh4+vO3x8XcXtxbI83Qe6HZcqZKWFhBCJjb1+i1hsxKPX1CpNmQx7LWDtMFrAwOydhGUKo4wWW4m9WCRp2bxXWOjrFbe7u/m+4LvspE6EEKWqXL+lrFxmjHg6yjKV1AF/VmDt8DcABmbvLCh5bJTRQgip7RWkUMoDA8J1nypVivz8dGenF90vy8XZmxCS+vCmT+1gQohKpUxMuuLo+Nw7rHCkKlfbO+LPCqwdFoXBwDx8JBql0kg//M2ocXG3/7x0db9Go0lKufbTztnrt4xXKstf8C3OTh716jY9fHxdTu5DpbL8p11zeHwj/m+vkCs9/WyM9/MBzAJGCxhYvVBp7mNjnTMYUK/55DE/Jqdcn7+s+4YfJ5aVy4Z+uFwkeslL+cD+83x9Gq5c89HsRV3sbJ1aNe+l/d/ViA1Oliur16jqNcQArA0uTwmGt+vbx1J3ZztXCe0gpqZSaJIuPxy1OIB2EADKsNcChte4naOswBrvTVKSU9q4rTPtFAD0oW8Ew2vYyuFibK6itoPY9tn/g125Fvt77IpnfkmtVgoEzz4t5oP+XzRq0N5QIVPS4jZu+/SZX1KpFEKBiPzv3sMVvff23CaNOj/vZz65m9NrMHZZALAgBsaReKPk8rFi71CPZ361rExWKi985pdK5cVS22dfgszezlUsNuQiW17+k+fEK5FI7J/5JTs7F5vnnBaTk5Lv68dv05PduwkAmAxGCxjL4a1ZxMZe4mgVh0upFZrclKx3J/nQDgLABHQtYCzdB3s8vJmpMs7pk6x5cOnhW8O9aKcAYAVGCxjRoJl+j+IyaKcwukdx6b1HedvaW+ktagCehgUxMC5Fmeb72UlB7XzFUqNcs5IurUabcuVx3zHeLp4W+NsBvDKMFjA6jYb8tDTVpY6LQy072lkMqSRHnhaXMXCan4sH5gpAJRgtYCJ/7ctN+Ke4VoCro4fZDxhZflluSl4tH3GPj190+TIAq4XRAqZTlKs8+3tuUaFGIBLaudqZ3en68qLyktzS8uIyGwmvQ183Lz8zyw9gMhgtYGoF2cqUO7L712VqNZEVKsUSodRRrFIZ66JeHIlsBaX55Qq5WmInIEQb2NQuINTe3ceI96kEsAAYLUCNSkFKi5WyIrW8RK0sZ3S0CMV8iZRv5ySUOgjEEhxRCVAtGC0AAGBgeBcGAAAGhtECAAAGhtECAAAGhtECAAAGhtECAAAGhtECAAAG9v/PZdNCYa/5iQAAAABJRU5ErkJggg==", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from email_assistant.utils import show_graph\n", "\n", "# Conditional edge function\n", "def should_continue(state: State) -> Literal[\"interrupt_handler\", \"__end__\"]:\n", "    \"\"\"Route to tool handler, or end if Done tool called\"\"\"\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    if last_message.tool_calls:\n", "        for tool_call in last_message.tool_calls: \n", "            if tool_call[\"name\"] == \"Done\":\n", "                return END\n", "            else:\n", "                return \"interrupt_handler\"\n", "\n", "# Build workflow\n", "agent_builder = StateGraph(State)\n", "\n", "# Add nodes\n", "agent_builder.add_node(\"llm_call\", llm_call)\n", "agent_builder.add_node(\"interrupt_handler\", interrupt_handler)\n", "\n", "# Add edges\n", "agent_builder.add_edge(START, \"llm_call\")\n", "agent_builder.add_conditional_edges(\n", "    \"llm_call\",\n", "    should_continue,\n", "    {\n", "        \"interrupt_handler\": \"interrupt_handler\",\n", "        END: END,\n", "    },\n", ")\n", "\n", "# Compile the agent\n", "response_agent = agent_builder.compile()\n", "\n", "# Build overall workflow\n", "overall_workflow = (\n", "    StateGraph(State, input=StateInput)\n", "    .add_node(triage_router)\n", "    .add_node(triage_interrupt_handler)\n", "    .add_node(\"response_agent\", response_agent)\n", "    .add_edge(START, \"triage_router\")\n", "    \n", ")\n", "\n", "email_assistant = overall_workflow.compile()\n", "show_graph(email_assistant, xray=True)"]}, {"cell_type": "markdown", "id": "d747dcda", "metadata": {}, "source": ["#### Review of HITL Patterns\n", "\n", "**Triage Interruption** When an email is classified as \"notify\", the system interrupts to show the email to the human user\n", "- *User Decision*: User can choose to ignore the notification or provide feedback to respond to the email\n", "- *Flow Control*: If ignored, workflow ends; if user provides feedback, it flows to the Response Agent\n", "\n", "**Write Email**: System shows proposed email draft for human review\n", "- *User Decision and Flow Control*: ignore (end workflow), respond with feedback, accept draft as-is, or edit draft\n", "\n", "**Schedule Meeting**: System shows proposed meeting details for human review\n", "- *User Decision and Flow Control*: ignore (end workflow), respond with feedback, accept meeting details as-is, or edit details\n", "\n", "**Question**: System asks user a question to clarify information\n", "- *User Decision and Flow Control*: ignore (end workflow) or respond with an answer\n", "\n", "### Interrupts Allow Us to Review and Accept Tool Calls"]}, {"cell_type": "code", "execution_count": null, "id": "c12b2097", "metadata": {}, "outputs": [], "source": ["import uuid\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "# Email to respond to\n", "email_input_respond = {\n", "    \"to\": \"<PERSON> <<EMAIL>>\",\n", "    \"author\": \"Project Manager <<EMAIL>>\",\n", "    \"subject\": \"Tax season let's schedule call\",\n", "    \"email_thread\": \"<PERSON>,\\n\\nIt's tax season again, and I wanted to schedule a call to discuss your tax planning strategies for this year. I have some suggestions that could potentially save you money.\\n\\nAre you available sometime next week? Tuesday or Thursday afternoon would work best for me, for about 45 minutes.\\n\\nRegards,\\nProject Manager\"\n", "}\n", "\n", "# Compile the graph with checkpointer\n", "checkpointer = InMemorySaver()\n", "graph = overall_workflow.compile(checkpointer=checkpointer)\n", "thread_id_1 = uuid.uuid4()\n", "thread_config_1 = {\"configurable\": {\"thread_id\": thread_id_1}}\n", "\n", "# Run the graph until a tool call that we choose to interrupt\n", "print(\"Running the graph until the first interrupt...\")\n", "for chunk in graph.stream({\"email_input\": email_input_respond}, config=thread_config_1):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "5546ad46", "metadata": {}, "source": ["What happened? We hit the [interrupt](https://langchain-ai.github.io/langgraph/concepts/interrupts/), which paused execution at the tool call. You can see the `action` (tool call name) and `args` (tool call arguments) that we interrupted displayed to the user.\n", "\n", "Now, how do we handle the interrupt? This is where the `Command` interface comes in. [The `Command` object has several powerful capabilities](https://langchain-ai.github.io/langgraph/how-tos/command/). We used it to direct the flow of the graph in prior notebooks: \n", "- `goto`: Specifies which node to route to next\n", "- `update`: Modifies the state before continuing execution\n", "\n", "Here, we'll use it to resume the graph from the interrupted state:\n", "- `resume`: Provides the value to return from the interrupt call\n", "\n", "We can return whatever value our graph is designed to handle. In our case, the graph is designed to handle a list of dicts with a single key `type` that can be `accept`, `edit`, `ignore`, or `response`. So, we can simply pass `{\"type\": \"accept\"}` to the `resume` argument in order to tell the graph that we accept the tool call."]}, {"cell_type": "code", "execution_count": null, "id": "50b1f772", "metadata": {}, "outputs": [], "source": ["from langgraph.types import Command\n", "\n", "print(f\"\\nSimulating user accepting the {Interrupt_Object.value[0]['action_request']} tool call...\")\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"accept\"}]), config=thread_config_1):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "fd321c51", "metadata": {}, "outputs": [], "source": ["print(f\"\\nSimulating user accepting the {Interrupt_Object.value[0]['action_request']} tool call...\")\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"accept\"}]), config=thread_config_1):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c77baa35", "metadata": {}, "outputs": [], "source": ["state = graph.get_state(thread_config_1)\n", "for m in state.values['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "86b1ba30", "metadata": {}, "source": ["\n", "\n", "### Interrupts Allow Us to Edit Tool Calls\n", "\n", "This test demonstrates how human modification works in the HITL flow:\n", "1. We start with the same tax planning email as before\n", "2. The agent proposes a meeting with the same parameters\n", "3. This time, the user EDITS the meeting proposal to change:\n", "   - Duration from 45 to 30 minutes\n", "   - Meeting subject is made more concise\n", "4. The agent adapts to these changes when drafting the email\n", "5. The user further EDITS the email to be shorter and less formal\n", "6. The workflow completes with both modifications incorporated\n", "\n", "This scenario showcases one of the most powerful aspects of HITL: \n", "\n", "* Users can make precise modifications to agent actions before they are executed, ensuring the final outcome matches their preferences without having to handle all the details themselves."]}, {"cell_type": "code", "execution_count": null, "id": "3bfca1b4", "metadata": {}, "outputs": [], "source": ["# Same email as before\n", "email_input_respond = {\n", "    \"to\": \"<PERSON> <<EMAIL>>\",\n", "    \"author\": \"Project Manager <<EMAIL>>\",\n", "    \"subject\": \"Tax season let's schedule call\",\n", "    \"email_thread\": \"<PERSON>,\\n\\nIt's tax season again, and I wanted to schedule a call to discuss your tax planning strategies for this year. I have some suggestions that could potentially save you money.\\n\\nAre you available sometime next week? Tuesday or Thursday afternoon would work best for me, for about 45 minutes.\\n\\nRegards,\\nProject Manager\"\n", "}\n", "\n", "# Compile the graph with new thread\n", "checkpointer = InMemorySaver()\n", "graph = overall_workflow.compile(checkpointer=checkpointer)\n", "thread_id_2 = uuid.uuid4()\n", "thread_config_2 = {\"configurable\": {\"thread_id\": thread_id_2}}\n", "\n", "# Run the graph until the first interrupt - will be classified as \"respond\" and the agent will create a write_email tool call\n", "print(\"Running the graph until the first interrupt...\")\n", "for chunk in graph.stream({\"email_input\": email_input_respond}, config=thread_config_2):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "706ac0a6", "metadata": {}, "source": ["Edit the `schedule_meeting` tool call\n", "\n", "When the agent proposes the initial meeting schedule, we now simulate the user making modifications through the edit functionality. This demonstrates how the `edit` response type works:\n", "\n", "1. The user receives the same meeting proposal as in the previous test\n", "2. Instead of accepting, they modify the parameters:\n", "   - Reducing duration from 45 to 30 minutes\n", "   - Keeping the same day and time\n", "3. The `edit` response includes the complete set of modified arguments\n", "4. The interrupt handler replaces the original tool arguments with these edited ones\n", "5. The tool is executed with the user's modifications\n", "\n", "This shows how edit capability gives users precise control over agent actions while still letting the agent handle the execution details."]}, {"cell_type": "code", "execution_count": null, "id": "7175fedb", "metadata": {}, "outputs": [], "source": ["# Now simulate user editing the schedule_meeting tool call\n", "print(\"\\nSimulating user editing the schedule_meeting tool call...\")\n", "edited_schedule_args = {\n", "    \"attendees\": [\"<EMAIL>\", \"<EMAIL>\"],\n", "    \"subject\": \"Tax Planning Discussion\",\n", "    \"duration_minutes\": 30,  # Changed from 45 to 30\n", "    \"preferred_day\": \"2025-05-06\",\n", "    \"start_time\": 14 \n", "}\n", "\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"edit\", \"args\": {\"args\": edited_schedule_args}}]), config=thread_config_2):\n", "    # Inspect response_agent most recent message\n", "    if 'response_agent' in chunk:\n", "        chunk['response_agent']['messages'][-1].pretty_print()\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "9757706b", "metadata": {}, "source": ["Edit the `write_email` tool call\n", "\n", "After accepting the modified meeting schedule, the agent drafts an email reflecting the 30-minute duration. Now we demonstrate how editing works with email content:\n", "\n", "1. The agent has adapted its email to mention the shorter 30-minute duration\n", "2. We simulate the user wanting an even more significant change to the email:\n", "   - Completely rewriting the content to be shorter and less formal\n", "   - Changing the meeting day mentioned in the email (showing how users can correct agent mistakes)\n", "   - Requesting confirmation rather than stating the meeting as definite\n", "3. The `edit` response contains the complete new email content\n", "4. The tool arguments are updated with this edited content\n", "5. The email is sent with the user's preferred wording\n", "\n", "This example shows the power of HITL for complex communication tasks - the agent handles the structure and initial content, while humans can refine tone, style, and substance."]}, {"cell_type": "code", "execution_count": null, "id": "a0604d53", "metadata": {}, "outputs": [], "source": ["# Now simulate user editing the write_email tool call\n", "print(\"\\nSimulating user editing the write_email tool call...\")\n", "edited_email_args = {\n", "    \"to\": \"<EMAIL>\",\n", "    \"subject\": \"Re: Tax season let's schedule call\",\n", "    \"content\": \"Hello Project Manager,\\n\\nThank you for reaching out about tax planning. I scheduled a 30-minute call next Thursday at 3:00 PM. Would that work for you?\\n\\nBest regards,\\nLance Martin\"\n", "}\n", "\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"edit\", \"args\": {\"args\": edited_email_args}}]), config=thread_config_2):\n", "    # Inspect response_agent most recent message\n", "    if 'response_agent' in chunk:\n", "        chunk['response_agent']['messages'][-1].pretty_print()\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "ac279101", "metadata": {}, "source": ["Look at the full message history, and see trace, to view the edited tool calls:\n", "\n", "https://smith.langchain.com/public/21769510-d57a-41e4-b5c7-0ddb23c237d8/r"]}, {"cell_type": "code", "execution_count": null, "id": "6d3e9be7", "metadata": {}, "outputs": [], "source": ["state = graph.get_state(thread_config_2)\n", "for m in state.values['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "da4c39e9", "metadata": {}, "source": ["### Interrupts Allow Us to Provide Fe<PERSON>back on Tool Calls\n", "\n", "This test set demonstrates the \"response\" capability - providing feedback without editing or accepting:\n", "\n", "1. First, we test feedback for meeting scheduling:\n", "   - The user provides specific preferences (30 minutes instead of 45, and afternoon meetings)\n", "   - The agent incorporates this feedback into a revised proposal\n", "   - The user then accepts the revised meeting schedule\n", "\n", "2. Second, we test feedback for email drafting:\n", "   - The user requests a shorter, less formal email with a specific closing statement\n", "   - The agent completely rewrites the email according to this guidance\n", "   - The user accepts the new draft\n", "\n", "3. Lastly, we test feedback for questions:\n", "   - For the brunch invitation, the user answers the question with additional context\n", "   - The agent uses this information to draft an appropriate email response\n", "   - The workflow proceeds with the user's input integrated\n", "\n", "The \"response\" capability bridges the gap between acceptance and editing - users can guide the agent without having to write the full content themselves. This is especially powerful for:\n", "- Adjusting tone and style\n", "- Adding context the agent missed\n", "- Redirecting the agent's approach\n", "- Answering questions in a way that shapes the next steps"]}, {"cell_type": "code", "execution_count": null, "id": "3c4b3517", "metadata": {}, "outputs": [], "source": ["# Respond - Meeting Request Email\n", "email_input_respond = {\n", "    \"to\": \"<PERSON> <<EMAIL>>\",\n", "    \"author\": \"Project Manager <<EMAIL>>\",\n", "    \"subject\": \"Tax season let's schedule call\",\n", "    \"email_thread\": \"<PERSON>,\\n\\nIt's tax season again, and I wanted to schedule a call to discuss your tax planning strategies for this year. I have some suggestions that could potentially save you money.\\n\\nAre you available sometime next week? Tuesday or Thursday afternoon would work best for me, for about 45 minutes.\\n\\nRegards,\\nProject Manager\"\n", "}\n", "\n", "# Compile the graph\n", "checkpointer = InMemorySaver()\n", "graph = overall_workflow.compile(checkpointer=checkpointer)\n", "thread_id_5 = uuid.uuid4()\n", "thread_config_5 = {\"configurable\": {\"thread_id\": thread_id_5}}\n", "\n", "# Run the graph until the first interrupt \n", "# Email will be classified as \"respond\" \n", "# Agent will create a schedule_meeting and write_email tool call\n", "print(\"Running the graph until the first interrupt...\")\n", "for chunk in graph.stream({\"email_input\": email_input_respond}, config=thread_config_5):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "ae2bea0a", "metadata": {}, "source": ["Provide feedback for the `schedule_meeting` tool call\n", "\n", "Now we explore the feedback capability for meeting scheduling:\n", "\n", "1. The agent proposes the standard 45-minute meeting on Tuesday at 2:00 PM\n", "2. Instead of accepting or editing, we provide feedback in natural language\n", "3. Our feedback specifies two preferences:\n", "   - Shorter meeting (30 minutes instead of 45)\n", "   - Preference for afternoon meetings (after 2pm)\n", "4. The agent receives this feedback through the `response` type\n", "5. The interrupt handler adds this feedback as a message to the state\n", "6. The agent processes this feedback and generates a new tool call incorporating these preferences\n", "\n", "Unlike direct editing, which requires specifying the entire set of parameters, feedback allows users to express their preferences conversationally. The agent must then interpret this feedback and apply it appropriately to create a revised proposal."]}, {"cell_type": "code", "execution_count": null, "id": "9a916e10", "metadata": {}, "outputs": [], "source": ["print(f\"\\nSimulating user providing feedback for the {Interrupt_Object.value[0]['action_request']['action']} tool call...\")\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"response\", \"args\": \"Please schedule this for 30 minutes instead of 45 minutes, and I prefer afternoon meetings after 2pm.\"}]), config=thread_config_5):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "cf35f1a2", "metadata": {}, "source": ["Accept the `schedule_meeting` tool call after providing feedback"]}, {"cell_type": "code", "execution_count": null, "id": "2727fb0e", "metadata": {}, "outputs": [], "source": ["print(f\"\\nSimulating user accepting the {Interrupt_Object.value[0]['action_request']} tool call...\")\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"accept\"}]), config=thread_config_5):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "3ca470c5", "metadata": {}, "source": ["Now provide feedback for the `write_email` tool call\n", "\n", "After accepting the revised meeting schedule, the agent drafts an email. We now test feedback for email content:\n", "\n", "1. The agent's email is relatively formal and detailed\n", "2. We provide stylistic feedback requesting:\n", "   - A shorter, more concise email\n", "   - A less formal tone\n", "   - A specific closing statement about looking forward to the meeting\n", "3. The agent processes this feedback to completely rewrite the email\n", "4. The new draft is much shorter, more casual, and includes the requested closing\n", "\n", "This demonstrates the power of natural language feedback for content creation:\n", "- Users don't need to rewrite the entire email themselves\n", "- They can provide high-level guidance on style, tone, and content\n", "- The agent handles the actual writing based on this guidance\n", "- The result better matches user preferences while preserving the essential information\n", "\n", "The message history shows both the original and revised emails, clearly showing how the feedback was incorporated."]}, {"cell_type": "code", "execution_count": null, "id": "f5221d87", "metadata": {}, "outputs": [], "source": ["print(f\"\\nSimulating user providing feedback for the {Interrupt_Object.value[0]['action_request']['action']} tool call...\")\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"response\", \"args\": \"Shorter and less formal. Include a closing statement about looking forward to the meeting!\"}]), config=thread_config_5):\n", "    # Inspect response_agent most recent message\n", "    if 'response_agent' in chunk:\n", "        chunk['response_agent']['messages'][-1].pretty_print()\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "1266ec72", "metadata": {}, "source": ["Accept the `write_email` tool call after providing feedback"]}, {"cell_type": "code", "execution_count": null, "id": "0b4698c0", "metadata": {}, "outputs": [], "source": ["print(f\"\\nSimulating user accepting the {Interrupt_Object.value[0]['action_request']} tool call...\")\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"accept\"}]), config=thread_config_5):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "c270f52a", "metadata": {}, "source": ["Look at the full message history, and see the trace:\n", "\n", "https://smith.langchain.com/public/57006770-6bb3-4e40-b990-143c373ebe60/r\n", "\n", "We can see that user feedback in incorporated into the tool calls.  "]}, {"cell_type": "code", "execution_count": null, "id": "1daf10d6", "metadata": {}, "outputs": [], "source": ["state = graph.get_state(thread_config_5)\n", "for m in state.values['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "1d964e36", "metadata": {}, "source": ["### Interrupts Enable New Tools\n", "\n", "Now let's try an email that calls the `Question` tool to provide feedback\n", "\n", "Finally, we test how feedback works with the `Question` tool:\n", "\n", "1. For the brunch invitation email, the agent asks about preferred day and time\n", "2. Instead of ignoring, we provide a substantive response with additional context:\n", "   - Confirming we want to invite the people mentioned\n", "   - Noting we need to check which weekend works best\n", "   - Adding information about needing a reservation\n", "3. The agent uses this information to:\n", "   - Draft a comprehensive email response incorporating all our feedback\n", "   - Notice we didn't provide a specific day/time, so it suggests checking the calendar\n", "   - Include the detail about making a reservation\n", "4. The complete email reflects both the original request and our additional guidance\n", "\n", "This demonstrates how question responses can shape the entire workflow:\n", "- Questions let the agent gather missing information\n", "- User responses can include both direct answers and additional context\n", "- The agent integrates all this information into its next actions\n", "- The final outcome reflects the collaborative intelligence of both human and AI"]}, {"cell_type": "code", "execution_count": null, "id": "8827632a", "metadata": {}, "outputs": [], "source": ["# Respond\n", "email_input_respond = {\n", "    \"to\": \"<PERSON> <<EMAIL>>\",\n", "    \"author\": \"Partner <<EMAIL>>\",\n", "    \"subject\": \"Dinner?\",\n", "    \"email_thread\": \"Hey, do you want italian or indian tonight?\"}\n", "\n", "# Compile the graph\n", "checkpointer = InMemorySaver()\n", "graph = overall_workflow.compile(checkpointer=checkpointer)\n", "thread_id_6 = uuid.uuid4()\n", "thread_config_6 = {\"configurable\": {\"thread_id\": thread_id_6}}\n", "\n", "# Run the graph until the first interrupt\n", "print(\"Running the graph until the first interrupt...\")\n", "for chunk in graph.stream({\"email_input\": email_input_respond}, config=thread_config_6):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "1d9f7f1b", "metadata": {}, "source": ["Provide feedback for the `Question` tool call"]}, {"cell_type": "code", "execution_count": null, "id": "4979effd", "metadata": {}, "outputs": [], "source": ["print(f\"\\nSimulating user providing feedback for the {Interrupt_Object.value[0]['action_request']['action']} tool call...\")\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"response\", \"args\": \"Let's do indian.\"}]), config=thread_config_6):\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "76b4ba9b", "metadata": {}, "source": ["Accept the `write_email` tool call"]}, {"cell_type": "code", "execution_count": null, "id": "bfd34ec2", "metadata": {}, "outputs": [], "source": ["print(f\"\\nSimulating user accepting the {Interrupt_Object.value[0]['action_request']['action']} tool call...\")\n", "for chunk in graph.stream(Command(resume=[{\"type\": \"accept\"}]), config=thread_config_6):\n", "    # Inspect response_agent most recent message\n", "    if 'response_agent' in chunk:\n", "        chunk['response_agent']['messages'][-1].pretty_print()\n", "    # Inspect interrupt object if present\n", "    if '__interrupt__' in chunk:\n", "        Interrupt_Object = chunk['__interrupt__'][0]\n", "        print(\"\\nINTERRUPT OBJECT:\")\n", "        print(f\"Action Request: {Interrupt_Object.value[0]['action_request']}\")"]}, {"cell_type": "markdown", "id": "e214fe9e", "metadata": {}, "source": ["Look at the full message history, and see the trace:\n", "\n", "https://smith.langchain.com/public/f4c727c3-b1d9-47a5-b3d0-3451619db8a2/r\n", "\n", "We can see that user feedback in incorporated into the email response."]}, {"cell_type": "code", "execution_count": null, "id": "070393eb", "metadata": {}, "outputs": [], "source": ["state = graph.get_state(thread_config_6)\n", "for m in state.values['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "0cbec016-f08a-4984-abb9-07f428f5e69f", "metadata": {}, "source": ["### Deployment\n", "\n", "Let's create a local deployment of our email assistant with H<PERSON><PERSON> from `src/email_assistant/email_assistant_hitl.py`. \n", " \n", "As before, run `langgraph dev`, select `email_assistant_hitl` in Studio, and submit the e-mail:"]}, {"cell_type": "code", "execution_count": null, "id": "2609b7e4-2065-4641-a1e6-5960f399a5f5", "metadata": {}, "outputs": [], "source": ["{\n", "  \"author\": \"<PERSON> <<EMAIL>>\",\n", "  \"to\": \"<PERSON> <<EMAIL>>\",\n", "  \"subject\": \"Quick question about API documentation\",\n", "  \"email_thread\": \"<PERSON> <PERSON>,\\nI was reviewing the API documentation for the new authentication service and noticed a few endpoints seem to be missing from the specs. Could you help clarify if this was intentional or if we should update the docs?\\nSpecifically, I'm looking at:\\n- /auth/refresh\\n- /auth/validate\\nThanks!\\nAlice\"\n", "}"]}, {"cell_type": "markdown", "id": "e0fe48f4", "metadata": {}, "source": ["Our server it stateless. Threads with a local deployment are simply saved to the local filesystem (`.langgraph_api` in the project folder).\n", "\n", "With a [hosted](https://langchain-ai.github.io/langgraph/tutorials/deployment/#other-deployment-options) deployment, threads stored in Postgres.\n", "\n", "Interrupted threads are threads with status 'interrupted', and we can see the interrupt in Studio: \n", "\n", "![studio-img](img/studio-interrupt.png)\n", "\n", "We'll use a custom interface to view these interrupted threads, [Agent Inbox](https://dev.agentinbox.ai/). \n", "\n", "This interface is a nice way to edit, approve, ignore, or provide feedback on specific actions taken by LangGraph agents. \n", "\n", "If you go to [dev.agentinbox.ai](https://dev.agentinbox.ai/), you can easily connect to the graph:\n", "   * Graph name: the name from the `langgraph.json` file (`email_assistant_hitl`)\n", "   * Graph URL: `http://127.0.0.1:2024/`\n", "\n", "All interrupted threads run will then be visible: \n", "\n", "![agent-inbox-img](img/agent-inbox.png)\n", "\n", "Agent Inbox simply uses a `Command` with `resume`, as [shown with the SDK](https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/wait-user-input/#interacting-with-the-agent) above, the resume the graph."]}, {"cell_type": "markdown", "id": "4dd416e5", "metadata": {}, "source": []}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "main_language": "python", "notebook_metadata_filter": "-all"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}