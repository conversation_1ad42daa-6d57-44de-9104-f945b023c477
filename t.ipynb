{"cells": [{"cell_type": "code", "execution_count": 2, "id": "0bc0677d", "metadata": {}, "outputs": [], "source": ["from main import get_client, TeleBot\n", "import asyncio\n", "async def run():\n", "    client = get_client()\n", "\n", "    # Initialize Telegram client\n", "    client = await get_client()\n", "    bot = TeleBot(client)\n", "\n", "    channel_link = \"ocutesting\"\n", "    try:\n", " \n", "        channel_entity = await client.get_entity(channel_link)\n", "\n", "        messages = await client.get_messages(channel_entity, 1)\n", "        \n", "        message_dict = messages[0].to_dict()\n", "        for key, value in message_dict.items():\n", "            print(f\"{key} : {value}\")\n", "    finally:\n", "        await client.disconnect()\n", "        "]}, {"cell_type": "code", "execution_count": 4, "id": "effff662", "metadata": {}, "outputs": [{"data": {"text/plain": ["<coroutine object run at 0x0000027B140EB890>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["run()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}